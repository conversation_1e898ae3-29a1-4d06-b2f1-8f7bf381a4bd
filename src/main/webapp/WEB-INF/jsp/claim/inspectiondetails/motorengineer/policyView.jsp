<%--
    Document   : policy
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 1.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <jsp:include page="/WEB-INF/jsp/claim/common/policyDetailsModal.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/policypage.js?v1"></script>
    <style>

        .is-invalid {
            border: 2px solid red !important;
        }
        .is-valid {
            border: 2px solid green !important;
        }
        /* General Styling */
        .assessment-details {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 20px;
            color: #333;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .divider {
            border-top: 2px solid #ddd;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .col-half {
            flex: 0 0 48%;
        }

        .label {
            font-weight: bold;
            color: #555;
        }

        .value {
            font-size: 16px;
            color: #333;
        }

        .input-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .input-field {
            width: 100%;
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .button-group {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .btn {
            padding: 10px 15px;
            font-size: 14px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            color: #fff;
            width: 48%;
        }

        .sms-btn {
            background-color: #007bff; /* Blue */
        }

        .sms-btn:hover {
            background-color: #0056b3;
        }

        .start-btn {
            background-color: #28a745; /* Green */
        }

        .start-btn:hover {
            background-color: #218838;
        }

        .start-btn:focus,
        .sms-btn:focus {
            outline: none;
        }

        @media (max-width: 768px) {
            .col-half {
                flex: 0 0 100%;
                margin-bottom: 10px;
            }
        }

    </style>
    <script>
        showLoader();
    </script>
</head>
<body class="scroll">
<div class="container-fluid">
    <div class="row">
        <div class="alert alert-dismissible alert-box" id="success-alert" style="display: none;">
            <a type="button" class="close" data-dismiss="alert">&times;</a>
            <strong>Alert! </strong>
            <p class="mb-0" id="error_mg"></p>
        </div>
        <div class="col-sm-12 bg-dark py-2">
            <h6> Accident Details -
                <small style="color:#090; "><%=UtilityBean.sysDate("d MMM yyyy 'at' hh:mm a")%>
                </small>
                <%--                <small style="color:#036;"><%=user.getV_usrid()%>--%>
                </small>
                <small class="ErrorNote text-danger float-right mt-2"></small>
                <small class="ErrorNote text-primary float-right"></small>
            </h6>
        </div>
    </div>
    <div>
        <div>
            <div class="ErrorNote"></div>
        </div>
        <form name="frmForm" id="frmForm" action="" method="post">
            <input type="hidden" id="P_POL_REF_NO" name="P_POL_REF_NO" value="${P_POL_REF_NO}">
            <input type="hidden" id="TYPE" name="TYPE" value="${TYPE}">
            <%--end new hidden fields--%>
            <div class="row">
                <div class="col-md-10 offset-1 scroll">
                    <div class="scroll" style="height: calc(100vh - 100px); overflow-x: hidden;">
                        <fieldset class="assessment-details">
                            <h6 class="section-title">Online Assessment Details</h6>
                            <hr class="divider">

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-half">
                                        <span class="label">Inspection type: </span>
                                        <span class="value">Online Assessment</span>
                                    </div>
                                    <div class="col-half">
                                        <span class="label">Job Id: </span>
                                        <span class="value" id="jobNo">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobId}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="contactNumberSendSms" class="input-label">Customer Contact Number:</label>
                                        <input type="text" class="form-control" id="contactNumberSendSms" value="${claimsDto.policyDto.custMobileNo}" />
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-primary mt-4" type="button" id="startJob" onclick="startOnMiSiteAssessment()">
                                            Start Job
                                        </button>
                                    </div>
                                </div>
                            </div>


                        </fieldset>

                        <fieldset class=" border p-2 mt-2">
                            <h6>Customer Details </h6>
                            <hr class="my-2">
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Customer Name : </span>
                                        <span class="label_Value" id="custName">${claimsDto.policyDto.custName}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Customer NIC Number : </span>
                                        <span class="label_Value"
                                              id="customerNicNo">${claimsDto.policyDto.custNic} </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span class="float-left" style="height: 100%;">Contact Address : </span>
                                        <span class="label_Value d-block"
                                              id="contactAddress">${claimsDto.policyDto.custAddressLine1} </span>
                                        <span class="label_Value"
                                              id="contactAddress2">${claimsDto.policyDto.custAddressLine2} </span>
                                        <span class="label_Value"
                                              id="contactAddress3">${claimsDto.policyDto.custAddressLine3} </span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Customer Contact Number : </span>
                                        <span class="label_Value "
                                              id="contactNumber">${claimsDto.policyDto.custMobileNo} </span>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="border p-2 mt-1">
                            <%--                                    <div class="form-group" >--%>
                            <div class="row">
                                <div class="col-sm-2"
                                     style="display: flex; justify-content: flex-start; align-items: center;">
                                    <span>Product Name : </span>
                                </div>
                                <div class="col-sm-8"
                                     style="display: flex; justify-content: center; align-items: center;">
                                    <h6 class="font-weight-bold m-0 p-2 border rounded"
                                        style="background: rgba(135, 206, 250, 0.3); text-align: center;">
                                        ${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }</h6>
                                </div>
                            </div>
                            <%--                                    </div>--%>
                        </fieldset>
                        <fieldset class="border p-2 mt-1">
                            <h6> Vehicle Details</h6>
                            <hr class="my-2">
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>Endorsement Count : </span>
                                        <span class="label_Value"
                                              id="endorsementCount">${claimsDto.policyDto.endCount}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <span>Renewal Count :</span>
                                        <span class="label_Value"
                                              id="renewalCount">${claimsDto.policyDto.renCount}</span>
                                    </div>
                                </div>
                                <hr class="m-0 mt-2">
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Vehicle No : </span>
                                        <span class="label_Value"
                                              id="vehicleNo">${claimsDto.policyDto.vehicleNumber}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Policy Number :</span>
                                        <span class="label_Value"
                                              id="policyNo">${claimsDto.policyDto.policyNumber}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Model Code :</span>
                                        <span class="label_Value"
                                              id="modelCode">${claimsDto.policyDto.vehicleModel}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Make Code :</span>
                                        <span class="label_Value"
                                              id="makeCode">${claimsDto.policyDto.vehicleMake}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Vehicle Color :</span>
                                        <span class="label_Value" id="">${claimsDto.policyDto.vehicleColor}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Engine Number :</span>
                                        <span class="label_Value" id="engineNo"> ${claimsDto.policyDto.engineNo}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Chassis Number :</span>
                                        <span class="label_Value"
                                              id="chassisNo">${claimsDto.policyDto.chassisNo} </span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Cover Note No :</span>
                                        <span class="label_Value"
                                              id="coverNoteNo">${claimsDto.policyDto.coverNoteNo}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Body Type :</span>
                                        <span class="label_Value" id="bodyType">${claimsDto.policyDto.bodyType}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Fuel Type :</span>
                                        <span class="label_Value" id="fuelType">${claimsDto.policyDto.fuelType}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>No of Seats :</span>
                                        <span class="label_Value" id="noOfSeats">${claimsDto.policyDto.noOfSeat}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Capacity Cylinder :</span>
                                        <span class="label_Value"
                                              id="capacityCylinder ">${claimsDto.policyDto.engCapacity}</span>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="border p-2 mt-1">
                            <h6> Policy Details</h6>
                            <hr class="my-2">
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Usage :</span>
                                        <span class="label_Value"
                                              id="usageofVehicle">${claimsDto.policyDto.vehicleUsage}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Cover Type :</span>
                                        <span class="label_Value" id="coverType">${claimsDto.policyDto.coverType}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Sum Insured (Rs.) :</span>
                                        <span class="label_Value"
                                              id="sumInsured">
                                            <fmt:formatNumber
                                                    value="${claimsDto.policyDto.sumInsured}"
                                                    pattern="###,##0.00;(###,##0.00)" type="number"/>
                                            </span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Manufacture Year:</span>
                                        <span class="label_Value"
                                              id="Manufactureyear">${claimsDto.policyDto.manufactYear}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Vehicle Age :</span>
                                        <span class="label_Value"
                                              id="vehicleAge ">${claimsDto.policyDto.vehicleAge}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Lease Company and Branch :</span>
                                        <a href="" class="label_Value pointer" style="text-decoration: underline;"
                                           id="leaseCompanyAndBranch" data-toggle="modal"
                                           data-target="#mortgageDetailsModal">${claimsDto.policyDto.financeCompany}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Annual Premium (Premium Break Up) (Rs.) :</span>
                                        <a href="" class="label_Value pointer" style="text-decoration: underline;"
                                           id="annualPremium" data-toggle="modal"
                                           data-target="#annualpreModal">
                                            <fmt:formatNumber
                                                    value="${claimsDto.policyDto.annualPremium}"
                                                    pattern="###,##0.00;(###,##0.00)" type="number"/>
                                        </a>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Outstanding Premium (Rs.) :</span>
                                        <span class="label_Value"
                                              id="premiumOutstanding">
                                            <fmt:formatNumber
                                                    value=" ${claimsDto.policyDto.totPremOutstand}"
                                                    pattern="###,##0.00;(###,##0.00)" type="number"/>
                                           </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Paid Amount (Rs.) :</span>
                                        <a href="" class="label_Value pointer" id="paidAmount"
                                           data-toggle="modal"
                                           style="text-decoration: underline;"
                                           data-target="#paidAmountModal">
                                            <fmt:formatNumber
                                                    value="${claimsDto.policyDto.paidTotalAmount}"
                                                    pattern="###,##0.00;(###,##0.00)" type="number"/>
                                        </a>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Days :</span>
                                        <span class="label_Value "
                                              id="days">${claimsDto.policyDto.noDayPremOutstand}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group bg-warning p-2">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Policy Period :</span>
                                        <span class="label_Value"
                                              id="policyPeriod">${claimsDto.policyDto.inspecDate}
                                        <c:if test="${claimsDto.policyDto.coverNoteNo ne claimsDto.policyDto.policyNumber}">
                                            to ${claimsDto.policyDto.expireDate}
                                        </c:if></span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Policy Status :</span>
                                        <span class="label_Value"
                                              id="policyStaus">${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimsDto.policyDto.polStatus)}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>NCB Rate:</span>
                                        <span class="label_Value"
                                              id="ncbRate">${claimsDto.policyDto.ncbRate}%</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <span>NCB Value (Rs.) :</span>
                                        <span class="label_Value"
                                              id="ncbAmount">
                                        <fmt:formatNumber value=" ${claimsDto.policyDto.ncbAmount}"
                                                          pattern="###,##0.00;(###,##0.00)" type="number"/>
                                       </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>Excess Amount (Rs.) :</span>
                                        <span class="label_Value" id="excessAmount">
                                        <fmt:formatNumber value="${claimsDto.policyDto.excess}"
                                                          pattern="###,##0.00;(###,##0.00)" type="number"/>
                                        </span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Product :</span>
                                        <span class="label_Value"
                                              id="product">${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }</span>
                                    </div>

                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Last Modified Date :</span>
                                        <span class="label_Value"
                                              id="lastModifiedDate">${claimsDto.policyDto.lastModifyDate}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <span>Last Modified User :</span>
                                        <span class="label_Value"
                                              id="lastModifiedUser">${claimsDto.policyDto.lastModifyUser}</span>
                                    </div>

                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span>Deduction :</span>
                                        <span class="label_Value"
                                              id="deductions">${claimsDto.policyDto.deduction}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <span>Business Type :</span>
                                        <span class="label_Value"
                                              id="businessType">${claimsDto.policyDto.bizType}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>Transaction Effect :</span>
                                        <span class="label_Value"
                                              id="transactionEffect">${claimsDto.policyDto.inspecDate}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <span>Issue Date :</span>
                                        <span class="label_Value"
                                              id="issueDate">${claimsDto.policyDto.createDate}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>Expiry status :</span>
                                        <span class="label_Value"
                                              id="polStatus"></span>
                                    </div>
                                    <script type="text/javascript">
                                        let exstatus = '${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimsDto.policyDto.currentPolStatus)}';
                                        if ('' === exstatus || null == exstatus) {
                                            $('#polStatus').text('${claimsDto.policyDto.currentPolStatus}');
                                        } else {
                                            $('#polStatus').text(exstatus);
                                        }
                                    </script>
                                    <div class="col-sm-6">
                                        <span>Branch :</span>
                                        <span class="label_Value"
                                              id="policyBranch">${claimsDto.policyDto.policyBranch}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <span>Issue Branch :</span>
                                        <span class="label_Value"
                                              id="branchCode">${claimsDto.policyDto.branchCode}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <span>Workflow :</span>
                                        <span class="label_Value"
                                              id="workflow">${claimsDto.policyDto.workflow}</span>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="border p-2 mt-1 d-flex justify-content-center">
                            <button class="btn btn-primary mr-1" type="button" name="btnTrailerDetails"
                                    id="btnTrailerDetails" onclick="getData('#trailerDetailsModal','TrailerDetails')">
                                Trailer Details
                            </button>
                            <button class="btn btn-primary ml-1" type="button" name="btnTradePlate"
                                    id="btnTradePlate" onclick="getData('#tradePlateModal','TradePlate')">Trade Plate
                            </button>
                        </fieldset>
                        <fieldset class="border p-2 mt-1">
                            <h6> Other Details </h6>
                            <hr class="my-2">

                            <div id="policy-memo-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="PolicyMemo">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#PolicyMemo_Details"
                                               aria-expanded="false" aria-controls="PolicyMemo_Details">
                                                Policy Memo
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="PolicyMemo_Details" aria-labelledby="PolicyMemo_Details"
                                         data-parent="#policy-memo-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%--                                                <p class="m-0"><b>Policy Memo</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Memo
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Position
                                                                        Flag
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Exclusion
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Order
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Delete
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="policyMemoDto"
                                                                           items="${claimsDto.policyDto.policyMemoDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${policyMemoDto.memo}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${policyMemoDto.position}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${policyMemoDto.memoDate}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${policyMemoDto.exclusion}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${policyMemoDto.order}</td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${policyMemoDto.delete eq 'Y' ? '<input type="checkbox" checked onclick="return false;">'
                                                                                : '<input type="checkbox" readonly onclick="return false;" >'}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="cwe-detail-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="CWEDetail">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#CWE_Detail"
                                               aria-expanded="false" aria-controls="CWE_Detail">
                                                CWE Details
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="CWE_Detail" aria-labelledby="CWE_Detail"
                                         data-parent="#cwe-detail-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%-- <p class="m-0"><b>CWE Detail</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header"></th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Description
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Entered Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Delete
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Deleted Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Order
                                                                        No
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="cweBody">
                                                                <c:forEach var="CweDetailDto"
                                                                           items="${claimsDto.policyDto.cweDetailDtoList}">
                                                                    <tr>
                                                                        <td scope="col" class="tbl_row_header">
                                                                            <button class="btn-primary btn btn-sm float-right btn-xs"
                                                                                    data-toggle="modal"
                                                                                    data-target="#${CweDetailDto.order}${CweDetailDto.endorsementCount}"
                                                                                    type="button"
                                                                                    title="Cwe Detail">
                                                                                <i class="fa fa-plus"
                                                                                   aria-hidden="true"></i>
                                                                            </button>
                                                                        </td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${CweDetailDto.header}</td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${CweDetailDto.enteredDate}</td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${CweDetailDto.isDeleted eq 'Y' ? '<input type="checkbox" checked onclick="return false;">'
                                                                                : '<input type="checkbox" readonly onclick="return false;" >'}</td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${CweDetailDto.deletedDate}</td>
                                                                        <td scope="row"
                                                                            class="tbl_row_header">${CweDetailDto.order}</td>
                                                                    </tr>
                                                                    <div class="modal fade"
                                                                         id="${CweDetailDto.order}${CweDetailDto.endorsementCount}">
                                                                        <div class="modal-dialog modal-lg"
                                                                             style="min-width:52%">
                                                                            <div class="modal-content">
                                                                                <!-- Modal body -->
                                                                                <div class="modal-body">
                                                                                    <div>
                                                                                        <h4 class="text-center font-weight-bold">
                                                                                            Additional Information</h4>
                                                                                        <hr/>
                                                                                        <h5 style="line-height: 1.6">
                                                                                            <pre>${CweDetailDto.text}</pre>
                                                                                        </h5>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="introducer_detail-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="IntroducerDetail">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               onclick="getIntroducerDetails()"
                                               data-target="#Introducer_Detail"
                                               aria-expanded="false" aria-controls="Introducer_Detail">
                                                Introducer Detail
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Introducer_Detail" aria-labelledby="Introducer_Detail"
                                         data-parent="#introducer_detail-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%--                                            <p class="m-0"><b>Introducer Detail</b></p>--%>
                                                    <br>
                                                    <%--                                            <fieldset class="border p-2 mt-1">--%>
                                                    <div class="form-group ">
                                                        <div class="row">
                                                            <div class="col-sm-6">
                                                                <span>Introducer Code :</span>
                                                                <span class="label_Value" id="agentCode"></span>
                                                            </div>
                                                            <div class="col-sm-6">
                                                                <span>Intorducer Name :</span>
                                                                <span class="label_Value" id="agentName"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group ">
                                                        <div class="row">
                                                            <div class="col-sm-6">
                                                                <span>Intorducer Mobile Number :</span>
                                                                <span class="label_Value" id="contactNo"></span>
                                                            </div>
                                                            <div class="col-sm-6">
                                                                <span>Intorducer E-Mail :</span>
                                                                <span class="label_Value" id="email"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <%--                                            </fieldset>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">Agent
                                                                        Code
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Agent
                                                                        Name
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Rank
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Rank
                                                                        Desc.
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Percent
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Status
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Start
                                                                        Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">End
                                                                        Date
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="introducerList"></tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="endorsement-history-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="EndorsementHistory">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Endorsement_History"
                                               aria-expanded="false" aria-controls="Endorsement_History">
                                                Endorsement History
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Endorsement_History" aria-labelledby="Endorsement_History"
                                         data-parent="#endorsement-history-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%--                                                            <p class="m-0"><b>Endorsement History</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header ">
                                                                        Policy Number
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Description
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="endorsementHistoryDto"
                                                                           items="${claimsDto.policyDto.endorsementHistoryDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${endorsementHistoryDto.policyNo}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${endorsementHistoryDto.description}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="billing-information-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="BillingInformation">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Billing_Information"
                                               aria-expanded="false" aria-controls="Billing_Information">
                                                Billing Information
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Billing_Information" aria-labelledby="Billing_Information"
                                         data-parent="#billing-information-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%--                                            <p class="m-0"><b>Billing Information</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">Bill
                                                                        No
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Renewal Count
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">End
                                                                        Count
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Bill
                                                                        Amount (Rs.)
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Bill
                                                                        Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Bill
                                                                        Status
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="billingInfoDto"
                                                                           items="${claimsDto.policyDto.billingInfoDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${billingInfoDto.billNo}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${billingInfoDto.renewCount}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${billingInfoDto.endCount}
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header"
                                                                            style="text-align: right;">
                                                                            <fmt:formatNumber
                                                                                    value="${billingInfoDto.billAmount}"
                                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                                    type="number"/>
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${billingInfoDto.billDate}
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${billingInfoDto.billStatus}
                                                                        </td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="claim-history-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="ClaimHistory">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Claim_History"
                                               aria-expanded="false" aria-controls="Claim_History">
                                                Claim History
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Claim_History" aria-labelledby="Claim_History"
                                         data-parent="#claim-history-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <hr class="my-2">
                                                <div class="form-group">
                                                    <%--                                                            <p class="m-0"><b>Claim History</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Accident Date
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Claim
                                                                        No
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Value
                                                                        Of Claim
                                                                        (Rs.)
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Status
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="claimHistory"
                                                                           items="${claimsDto.claimHistory}">
                                                                    <c:if test="${claimHistory.claimNo != claimsDto.claimNo}">
                                                                        <tr>
                                                                            <td scope="col"
                                                                                class="tbl_row_header">${claimHistory.accidDate}</td>
                                                                            <td scope="col"
                                                                                class="tbl_row_header">${claimHistory.claimNo}</td>
                                                                            <td scope="col"
                                                                                class="tbl_row_header text-right">
                                                                                    ${claimHistory.totalAcr}
                                                                            </td>
                                                                            <td scope="col"
                                                                                class="tbl_row_header">${DbRecordCommonFunctionBean.getValue("claim_status_para", "v_status_desc", "n_ref_id", claimHistory.claimStatus)}
                                                                                <button id="${claimHistory.claimNo}"
                                                                                        class="btn-primary btn btn-sm float-right btn-xs"
                                                                                        type="button"
                                                                                        title="View Claim History"
                                                                                        onclick='viewClaimHistory("${claimHistory.polRefNo}","${claimHistory.claimNo}")'>
                                                                                    <i class="fa fa-eye"></i>
                                                                                </button>
                                                                            </td>
                                                                        </tr>
                                                                    </c:if>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="benefits-covers-details-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="Benefits/CoversDetails">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Benefits_Covers_Details"
                                               aria-expanded="false" aria-controls="Benefits_Covers_Details">
                                                Benefits/Covers Details
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Benefits_Covers_Details" aria-labelledby="Benefits_Covers_Details"
                                         data-parent="#benefits-covers-details-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <hr class="my-2">
                                                <div class="form-group">
                                                    <%--                                                            <p class="m-0"><b> Benefits/Covers Details</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">Cover
                                                                        Details
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Cover
                                                                        Amount (Rs.)
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Rate%
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="coverDto"
                                                                           items="${claimsDto.policyDto.coverDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${coverDto.coverDesc}</td>
                                                                        <td scope="col" class="tbl_row_header"
                                                                            style="text-align: right">
                                                                            <fmt:formatNumber
                                                                                    value="${coverDto.coverAmount}"
                                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                                    type="number"/>
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${coverDto.coverRate}%
                                                                        </td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="loading-excess-details-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="Loading/ExcessDetails">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Loading_Excess_Details"
                                               aria-expanded="false" aria-controls="Loading_Excess_Details">
                                                Loading/Excess Details
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Loading_Excess_Details" aria-labelledby="Loading_Excess_Details"
                                         data-parent="#loading-excess-details-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <hr class="my-2">
                                                <div class="form-group">
                                                    <%--                                                            <p class="m-0"><b>Loading/Excess Details</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Excess Details
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Excess Amount (Rs.)
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Rate%
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="excessDto"
                                                                           items="${claimsDto.policyDto.excessDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${excessDto.excessDesc}</td>
                                                                        <td scope="col" class="tbl_row_header"
                                                                            style="text-align: right;">
                                                                            <fmt:formatNumber
                                                                                    value="${excessDto.excessAmount}"
                                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                                    type="number"/>
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${excessDto.excessRate}%
                                                                        </td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="learner-driver-details-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="LearnerDriverDetails">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#Learner_Driver_Details"
                                               aria-expanded="false" aria-controls="Learner_Driver_Details">
                                                Learner Driver Details
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="Learner_Driver_Details" aria-labelledby="Learner_Driver_Details"
                                         data-parent="#learner-driver-details-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <hr class="my-2">
                                                <div class="form-group">
                                                    <%--                                                            <p class="m-0"><b>Learner Driver Details</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">NIC
                                                                        No
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">ID
                                                                        Type
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Name
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">Age
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Premium
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <c:forEach var="learnerDriverDetailsDto"
                                                                           items="${claimsDto.policyDto.learnerDriverDetailsDtoList}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${learnerDriverDetailsDto.nicNo}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${learnerDriverDetailsDto.idType}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${learnerDriverDetailsDto.name}
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${learnerDriverDetailsDto.age}
                                                                        </td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header"
                                                                            style="text-align: right;">
                                                                            <fmt:formatNumber
                                                                                    value="${learnerDriverDetailsDto.premium}"
                                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                                    type="number"/>
                                                                        </td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="ncb-history-details-card">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="NCBHistoryDetails">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse"
                                               data-target="#NCB_History_Details"
                                               aria-expanded="false" aria-controls="NCB_History_Details">
                                                NCB History Details
                                            </a>
                                        </h5>
                                    </div>

                                    <div id="NCB_History_Details" aria-labelledby="NCB_History_Details"
                                         data-parent="#ncb-history-details-card"
                                         class="collapse">
                                        <div class="card-body p-0">
                                            <fieldset class="border p-2 mt-1">
                                                <div class="form-group">
                                                    <%--  <p class="m-0"><b>NCB History Details</b></p>--%>
                                                    <div class="row">
                                                        <div class="col">
                                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                <thead>
                                                                <tr>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Policy Year
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Renewal Count
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Endorsement Count
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">
                                                                        Status
                                                                    </th>
                                                                    <th scope="col" class="tbl_row_header">NCB
                                                                        (%)
                                                                    </th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="ncdHistory">
                                                                <c:forEach var="ncbHistoryDto"
                                                                           items="${claimsDto.policyDto.ncbHistoryDetailsSummary}">
                                                                    <tr>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${ncbHistoryDto.policyYear}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${ncbHistoryDto.renewalCount}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${ncbHistoryDto.endorsementCount}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${ncbHistoryDto.status}</td>
                                                                        <td scope="col"
                                                                            class="tbl_row_header">${ncbHistoryDto.ncbPer}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <fieldset class="border p-2 mt-1">
                        <div class="float-right">
                            <button type="button" name="cmdClose" id="cmdClose" value="Close"
                                    onclick="back('${TYPE}')" class="btn btn-link"><b> Back</b>
                            </button>

                        </div>
                        <%--<h6> Previous Remarks--%>
                        <%--</h6>--%>
                        <%--<hr class="my-2">--%>
                        <%--<div class="form-group">--%>
                        <%--<textarea name="txtV_prev_remark" id="txtV_prev_remark" title="Remarks"--%>
                        <%--class="form-control form-control-sm" readonly rows="6"></textarea>--%>
                        <%--</div>--%>
                    </fieldset>
                </div>
            </div>
            <input type="hidden" name="claimStatus" id="claimStatus" value="${claimsDto.claimStatus}">
            <input type="hidden" name="claimNo" id="claimNo" value="${claimsDto.claimNo}">
        </form>
    </div>
    <div id="dialog" style="display:none;" title="${CompanyTitle} Lanka PLC.">
        <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
        <p id="dialog-email" class="textGrey"></p>
    </div>
</div>

<div class="modal fade" id="tradePlateModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <fieldset class="border p-2 m-2">
                <div class="form-group">
                    <p class="m-0"><b>Trade Plate Detail</b></p>
                    <div class="row">
                        <div class="col">
                            <table width="100%" cellpadding="0" cellspacing="1"
                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                <thead>
                                <tr>
                                    <th scope="col" class="tbl_row_header">Trade Plate NO</th>
                                    <th scope="col" class="tbl_row_header">Description</th>
                                    <th scope="col" class="tbl_row_header">Delete</th>
                                </tr>
                                </thead>
                                <tbody id="tradePlateTbl">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
</div>

<div class="modal fade" id="trailerDetailsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <fieldset class="border p-2 m-2">
                <h6> Trailer Details </h6>
                <hr class="my-2">
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Trailer No :</span>
                            <span class="label_Value"
                                  id="trailerNo"></span>
                        </div>
                        <div class="col-sm-6">
                            <span>Sum Insured :</span>
                            <span class="label_Value"
                                  id="trailersumInsured"></span>
                        </div>
                    </div>
                </div>
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Auto Premium :</span>
                            <span class="label_Value"
                                  id="autoPremium"></span>
                        </div>
                        <div class="col-sm-6">
                            <span>Manual Premium :</span>
                            <span class="label_Value"
                                  id="manualPremium"></span>
                        </div>
                    </div>
                </div>
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Remarks:</span>
                            <span class="label_Value"
                                  id="trailerRemarks"></span>
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
</div>

<div class="modal fade" id="mortgageDetailsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <fieldset class="border p-2 m-2">
                <h6> Mortgage Details </h6>
                <hr class="my-2">
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Finance Company :</span>
                            <span class="label_Value"
                                  id="finCompany">${claimsDto.policyDto.financeCompany}</span>
                        </div>
                        <div class="col-sm-6">
                            <span>Finance Company Code :</span>
                            <span class="label_Value"
                                  id="finCompanyCode">${claimsDto.policyDto.finCompanyCode}</span>
                        </div>
                    </div>
                </div>
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Finance Company Branch :</span>
                            <span class="label_Value"
                                  id="finCompanyBranch">${claimsDto.policyDto.finCompanyBranch}</span>
                        </div>
                        <div class="col-sm-6">
                            <span>Bank Reference Number :</span>
                            <span class="label_Value"
                                  id="bankRefNumber">${claimsDto.policyDto.bankRefNo}</span>
                        </div>
                    </div>
                </div>
                <div class="form-group ">
                    <div class="row">
                        <div class="col-sm-6">
                            <span>Loan Account No :</span>
                            <span class="label_Value"
                                  id="loanAccNo">${claimsDto.policyDto.loanAccNo}</span>
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
</div>

<div class="modal fade" id="annualpreModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <c:set var="premiumBreakupFormDto" value="${claimsDto.policyDto.premiumBreakupFormDto}"
                               scope="request"/>
                        <c:set var="chargesBreakupDto"
                               value="${claimsDto.policyDto.premiumBreakupFormDto.chargesBreakupDto}" scope="request"/>
                        <table width="100%" cellpadding="0" cellspacing="1"
                               class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline ">
                            <thead>
                            <tr>
                                <th scope="col" class="tbl_row_header">Details</th>
                                <th scope="col" class="tbl_row_header">Description</th>
                                <th scope="col" class="tbl_row_header">Premium</th>
                                <th scope="col" class="tbl_row_header">Contribution & Changed</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>Riot & Strike</td>
                                <td>
                                    <c:forEach var="policyPremiumDto"
                                               items="${premiumBreakupFormDto.policyPremiumRiotStrikeList}">
                                        ${policyPremiumDto.description}<br>
                                    </c:forEach>
                                </td>
                                <c:set var="totalRiotStrike" value="0" scope="request"/>
                                <td class="text-right">
                                    <c:forEach var="policyPremiumDto"
                                               items="${premiumBreakupFormDto.policyPremiumRiotStrikeList}">
                                        <fmt:formatNumber
                                                value="${policyPremiumDto.premiumAmount}"
                                                pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                        <c:set var="totalRiotStrike"
                                               value="${totalRiotStrike+policyPremiumDto.premiumAmount}"
                                               scope="request"/>
                                    </c:forEach>
                                </td>
                                <td class="text-right vertical-bottom">
                                    <fmt:formatNumber
                                            value="${totalRiotStrike}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/>
                                </td>
                            </tr>
                            <tr>
                                <td>Commercial Vehicle
                                </td>
                                <td>
                                    <c:forEach var="policyPremiumDto"
                                               items="${premiumBreakupFormDto.policyPremiumDtoList}">
                                        ${policyPremiumDto.description}<br>
                                    </c:forEach>
                                </td>
                                <c:set var="totalCommercialVehicle" value="0" scope="request"/>
                                <td class="text-right">
                                    <c:forEach var="policyPremiumDto"
                                               items="${premiumBreakupFormDto.policyPremiumDtoList}">
                                        <fmt:formatNumber
                                                value="${policyPremiumDto.premiumAmount}"
                                                pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                        <c:set var="totalCommercialVehicle"
                                               value="${totalCommercialVehicle+policyPremiumDto.premiumAmount}"
                                               scope="request"/>
                                    </c:forEach>
                                </td>
                                <td class="text-right vertical-bottom">${totalCommercialVehicle}</td>
                            </tr>
                            <tr>
                                <td>Other Charges</td>
                                <td>Policy Fee<br>
                                    Stamp Duty<br>
                                    CESS<br>
                                    Road Tax<br>
                                    NBT<br>
                                    VAT
                                </td>
                                <td class="text-right">
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.pof}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.sd}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.cess}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.rt}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.nbt}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                    <fmt:formatNumber
                                            value="${chargesBreakupDto.vat}"
                                            pattern="###,##0.00;(###,##0.00)" type="number"/><br>
                                </td>
                                <td class="text-right vertical-bottom">${chargesBreakupDto.pof+chargesBreakupDto.sd+chargesBreakupDto.cess+chargesBreakupDto.rt+chargesBreakupDto.nbt+chargesBreakupDto.vat}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="paidAmountModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <table width="100%" cellpadding="0" cellspacing="1"
                               class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline ">
                            <thead>
                            <tr>
                                <th scope="col" class="tbl_row_header">Date</th>
                                <th scope="col" class="tbl_row_header">Receipt Number</th>
                                <th scope="col" class="tbl_row_header">Amount</th>
                                <th scope="col" class="tbl_row_header">Payment Mode</th>
                            </tr>
                            </thead>
                            <tbody id="receiptDetail">
                            <c:forEach var="paidDto" items="${claimsDto.policyDto.paidDetailsDtoList}">
                                <tr>
                                    <td scope="col" class="tbl_row_header">${paidDto.paidDate}</td>
                                    <td scope="col" class="tbl_row_header">${paidDto.receiptNumber}</td>
                                    <td scope="col" class="tbl_row_header"
                                        style="text-align: right">
                                        <fmt:formatNumber value="${paidDto.paidAmount}"
                                                          pattern="###,##0.00;(###,##0.00)" type="number"/>
                                    </td>
                                    <td scope="col" class="tbl_row_header">${paidDto.paymentMode}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%--Service Factor Model--%>
<div class="modal fade" id="serviceFactorModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <c:set var="premiumBreakupFormDto"
                               value="${claimsDto.policyDto.premiumBreakupFormDto}"
                               scope="request"/>
                        <c:set var="chargesBreakupDto"
                               value="${claimsDto.policyDto.premiumBreakupFormDto.chargesBreakupDto}"
                               scope="request"/>
                        <c:set var="productDetailListDto"
                               value="${claimsDto.policyDto.productDetailListDto}"
                               scope="request"/>

                        <table width="100%" cellpadding="0" cellspacing="1"
                               class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline ">
                            <thead>
                            <tr>
                                <th scope="col" class="tbl_row_header">Priority</th>
                                <th scope="col" class="tbl_row_header">Service Factors</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="serviceFactorData" items="${productDetailListDto.serviceFactorData}"
                                       varStatus="loop">
                                <tr>
                                    <td>${serviceFactorData.orderNo}</td>
                                    <td>${serviceFactorData.serviceFactorName}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="${pageContext.request.contextPath}/resources/rating/star-rating.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/claim/callcenter/jsAjaxGrid.js"></script>
<script type="text/javascript">

    $(document).ready(function () {
        $("#districtCode").on('change', function (evt, params) {
            var districtId = parseInt($(this).val());
            $.ajax({
                url: contextPath + "/CallCenter/getTableValueFromGivenCriteria?tableName=claim_district&valueField=V_DISTRICT_CODE&searchFiled=N_TXN_ID&searchValue=" + districtId
            }).success(function (data) {
                var districtCode = JSON.parse(data);
                $.ajax({
                    url: contextPath + "/AssessorAllocationController/citylist?divisonCode=" + districtCode
                }).success(function (data) {
                    var obj = JSON.parse(data);
                    $("#nearestCity").html("");
                    $("#nearestCity").append($('<option>').val("").html("--Select--")).trigger("chosen:updated");
                    for (var i = 0; i < obj.length; i++) {
                        $("#nearestCity").append($('<option>').val(obj[i].gramaCode).html(obj[i].gramaName)).trigger("chosen:updated");
                    }
                });
            });
        });
    });

    let trailerDetailsCount = 0;
    let tradePlateCount = 0;

    function getData(modal, value) {
        if ("TrailerDetails" === value && 0 === trailerDetailsCount) {
            trailerDetailsCount++;
            $.ajax({
                type: 'GET',
                url: contextPath + `/ClaimHandlerController/getTrailerDetail?PolicyNo=${claimsDto.policyDto.policyNumber}&PolicyChannelType=${claimsDto.policyDto.policyChannelType}`,
                success: function (value) {
                    let result = JSON.parse(value);
                    $("#trailerNo").append(result.trailerNo);
                    $("#trailersumInsured").append(result.sumInsured);
                    $("#autoPremium").append(result.autoPremium);
                    $("#manualPremium").append(result.manualPremium);
                    $("#trailerRemarks").append(result.remark);
                }
            });
        }

        if ("TradePlate" === value && 0 === tradePlateCount) {
            tradePlateCount++;
            $.ajax({
                type: 'GET',
                url: contextPath + `/ClaimHandlerController/getTradePlateDetail?PolicyNo=${claimsDto.policyDto.policyNumber}&PolicyChannelType=${claimsDto.policyDto.policyChannelType}`,
                success: function (value) {
                    let result = JSON.parse(value);
                    result.map((data) => {
                        let checkBox;
                        if (data.isDelete === "Y") {
                            checkBox = `<input type="checkbox" checked onclick="return false;">`;
                        } else {
                            checkBox = `<input type="checkbox" onclick="return false;">`;
                        }
                        $("#tradePlateTbl").append(`<tr><td scope="row" class="tbl_row_header">` + data.tradePlateNo + `</td><td scope="row" class="tbl_row_header">` + data.desc + `</td><td scope="row" class="tbl_row_header">` + checkBox + `</td></tr>`);
                    });
                }
            });
        }
        $(modal).modal('show')
    }

    let introducerCount = 0;

    function getIntroducerDetails() {
        if (0 === introducerCount) {
            introducerCount++;
            $("#agentCode").append('${claimsDto.policyDto.introducerDto.agentCode}');
            $("#agentName").append('${claimsDto.policyDto.introducerDto.agentName}');
            $("#contactNo").append('${claimsDto.policyDto.introducerDto.contactNo}');
            $("#email").append('${claimsDto.policyDto.introducerDto.email}');

            $("#introducerList").append(`<c:forEach var="sellingDto" items="${claimsDto.policyDto.sellingAgentDetailsDtoList}"><tr><td scope="col" class="tbl_row_header">${sellingDto.agentCode}</td><td scope="col" class="tbl_row_header">${sellingDto.agentName}</td>
                                      <td scope="col" class="tbl_row_header">${sellingDto.rank}</td><td scope="col" class="tbl_row_header">${sellingDto.rankDesc}</td><td scope="col" class="tbl_row_header">${sellingDto.percent}</td>
                                      <td scope="col" class="tbl_row_header">${sellingDto.agentStatus}</td><td scope="col" class="tbl_row_header">${sellingDto.startDate}</td><td scope="col" class="tbl_row_header">${sellingDto.endDate}</td></tr></c:forEach>`);
        }

    }

    function selectPolicyToSession() {
        window.parent.$.colorbox.close();
        window.parent.location.href = contextPath + "/CallCenter/setToSession";
    }

    function policyDetailsView() {
        document.frmForm.action = contextPath + "/CallCenter/viewReportedClaim?TYPE=4";
        document.frmForm.submit();
    }

    function viewClaimHistory(polRefNo, claimNo) {

        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
//        document.getElementById('frmForm').action = contextPath + "/CallCenter/viewClaimHistory";
//        document.getElementById('frmForm').submit();
    }
</script>

<script type="text/javascript">
    function validateMobileField() {
        let val = $('#contactNumberSendSms').val().trim();
        let isValid = /^07\d{8}$/.test(val); // starts with 07 + 8 more digits

        if (isValid) {
            $('#contactNumberSendSms')
                .removeClass('is-invalid')
                .addClass('is-valid');
        } else {
            $('#contactNumberSendSms')
                .removeClass('is-valid')
                .addClass('is-invalid');
        }
    }
    $('#contactNumberSendSms').on('input', validateMobileField);

    function startOnMiSiteAssessment() {
        if (!$('#contactNumberSendSms').val().trim()) {
            notify("Mobile number is mandatory to start the online assessment.", "danger");
            setTimeout(() => {
                $('#contactNumberSendSms')
                    .addClass('is-invalid') // red outline
                    .focus();
            }, 200);
            return;
        }

        showLoader()
        const requestPayload = {
            claimNumber: '${claimsDto.claimNo}',
            inspectionJobNumber: '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobId}',
            assignee: '',
            callingMobileNo: $('#contactNumberSendSms').val(),
            inspectionTypeId: '${motorEngineerDto.inspectionDetailsDto.inspectionId}',
            policyNo:'${claimsDto.policyDto.policyNumber}',
            vehicleNo: '${claimsDto.policyDto.vehicleNumber}',
            vehicleMake: '${claimsDto.policyDto.vehicleMake}',
            vehicleModel: '${claimsDto.policyDto.vehicleModel}',
            insuredName: '${claimsDto.policyDto.custName}',
            insuredAddress1:'${claimsDto.policyDto.custAddressLine1}',
            insuredAddress2:'${claimsDto.policyDto.custAddressLine1}',
            insuredAddress3: '${claimsDto.policyDto.custAddressLine1}',
            insuredMobileNo: '${claimsDto.policyDto.custMobileNo}',
            insuredEmail: ''
        };
        console.log("payload : ", requestPayload);

        $.ajax({

            type: 'POST',
            url: '${pageContext.request.contextPath}/MotorEngineerController/callOnMiSiteOnlineAssessment',
            contentType: 'application/json',
            data: JSON.stringify(requestPayload),
            success: function (response) {
                console.log("url", response);
                hideLoader()
                window.open(response, '_blank');
            },
            error: function (xhr) {
                hideLoader()
                notify('Failed to start job: Unable to communicate with the target system.', "danger");
            }
        });
    }

    hideLoader();
</script>

</body>
<script>
    hideLoader();
</script>
</html>

<%--claimNumber: 123456,--%>
<%--inspectionJobNumber: 'JOB-7890',--%>
<%--assignee: 'John Doe',--%>
<%--callingMobileNo: '0771234567',--%>
<%--inspectionTypeId: 3,--%>
<%--policyNo: 'POL-456789',--%>
<%--vehicleNo: 'CAX-1234',--%>
<%--vehicleMake: 'Toyota',--%>
<%--vehicleModel: 'Corolla',--%>
<%--insuredName: 'Michael Fernando',--%>
<%--insuredAddress1: '123 Main Street',--%>
<%--insuredAddress2: 'Colombo 05',--%>
<%--insuredAddress3: 'Western Province',--%>
<%--insuredMobileNo: '0771234567',--%>
<%--insuredEmail: '<EMAIL>'--%>
