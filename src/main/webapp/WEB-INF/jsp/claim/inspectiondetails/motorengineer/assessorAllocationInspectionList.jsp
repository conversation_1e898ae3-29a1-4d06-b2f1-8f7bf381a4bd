<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">


        $(function () {
            //
            //buttonImage: '/image/common/calendar.gif',
            var d1 = document.frmForm.txtFromDate.value;
            $("#txtFromDate").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: '1940:2099',
                minDate: '-70y',
                maxDate: '0d',
                onClose: function (dateText, inst) {
                    document.getElementById("txtToDate").focus();
                }

            });
            $("#txtFromDate").datepicker('option', {dateFormat: "yy-mm-dd"});
            document.frmForm.txtFromDate.value = d1;

        });
        $(function () {
            //
            //buttonImage: '/image/common/calendar.gif',
            var d1 = document.frmForm.txtToDate.value;
            $("#txtToDate").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: '1940:2099',
                minDate: '-70y',
                maxDate: '0d',
                onClose: function (dateText, inst) {
//                    document.getElementById("txtToDate").focus();
                }

            });
            $("#txtToDate").datepicker('option', {dateFormat: "yy-mm-dd"});
            document.frmForm.txtToDate.value = d1;

        });


        //------------Start JQuery Script---------------------


        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }


        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }


        function setLoading() {
            parent.document.getElementById("cell1").style.display = "block";
            parent.document.getElementById("loading").style.display = "block";

        }
    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="P_N_JOB_NO" id="P_N_JOB_NO" type="hidden"/>
        <input name="P_N_REF_NO" id="P_N_REF_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Allocated Inspections List</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls=0"collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Date </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Inspection
                                                Type</label>
                                            <div class="col-sm-8">
                                                <select name="txtInspectionType" id="txtInspectionType"
                                                        class="form-control form-control-sm">

                                                    <option value="0">Please Select One</option>
                                                    <option value="1">On site Inspection</option>
                                                    <option value="2">Off site Inspection</option>
                                                    <option value="3">Underwiting Inspection</option>
                                                    <option value="4">Garage Inspection</option>
                                                    <option value="5">DR Insepection</option>
                                                    <option value="6">Supplimantary Inspection</option>
                                                    <option value="7">After Repair inspection</option>
                                                    <option value="8">Desktop Assesment</option>
                                                    <option value="9">Salvage Inspection</option>
                                                    <option value="10">Investigation</option>
                                                    <option value="10">Investigation</option>
                                                    <option value="11">Call Estimate</option>
                                                    <option value="12">Remort Assessment</option>
                                                    <option value="13">UWI Repair inspection</option>
                                                    <option value="14">Others Repair inspection</option>
                                                    <option value="15">Survey Inspection</option>
                                                    <option value="16">Location inspection</option>
                                                    <option value="17">Additional inspection</option>
                                                    <option value="18">Vertual Onsite /Garage</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtJobNumber" class="col-sm-4 col-form-label"> Assessor Fee.
                                                Status</label>
                                            <div class="col-sm-8">


                                                <select name="txtAssessorApprStatus" id="txtAssessorApprStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="1">Approved</option>
                                                    <option value="2">Pending</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Date </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Cover Note
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <c:choose>
                                                        <c:when test="${ME_PAGE_TYPE eq 1 || ME_PAGE_TYPE eq 30 || ME_PAGE_TYPE eq 50 || ME_PAGE_TYPE eq 60}">
                                                            <option value="0">ALL</option>
                                                            <option value="29">ASSIGNED</option>
                                                            <option value="9">APPROVED</option>
                                                            <option value="7">ATTENDED</option>
                                                            <option value="10">CLAIM CHANGE REQUESTED</option>
                                                            <option value="14">INSPECTION CHANGE REQUESTED</option>
                                                            <option value="33">FORWARD FOR INFORM DESKTOP</option>
                                                            <option value="34">RETURN DESKTOP</option>
                                                            <option value="8" selected>SUBMITTED</option>
                                                            <option value="80">INSPECTION FORWARDED</option>
                                                        </c:when>
                                                        <c:when test="${ME_PAGE_TYPE eq 2}">
                                                            <option value="0" selected>ALL</option>
                                                            <option value="29">PENDING</option>
                                                            <option value="33">FORWARD FOR INFORM DESKTOP</option>
                                                            <option value="34">RETURNED DESKTOP INSPECTION</option>
                                                        </c:when>
                                                        <c:when test="${ME_PAGE_TYPE eq 70}">
                                                            <option value="80" selected>INSPECTION FORWARDED</option>
                                                        </c:when>
                                                    </c:choose>
                                                    <%--                                                    <c:if test="${ME_PAGE_TYPE eq 1 || ME_PAGE_TYPE eq 30 || ME_PAGE_TYPE eq 50 || ME_PAGE_TYPE eq 60}">--%>
                                                    <%--                                                        <option value="0">ALL</option>--%>
                                                    <%--                                                        <option value="29">ASSIGNED</option>--%>
                                                    <%--                                                        <option value="9">APPROVED</option>--%>
                                                    <%--                                                        <option value="7">ATTENDED</option>--%>
                                                    <%--                                                        <option value="10">CLAIM CHANGE REQUESTED</option>--%>
                                                    <%--                                                        <option value="14">INSPECTION CHANGE REQUESTED</option>--%>
                                                    <%--                                                        <option value="33">FORWARD FOR INFORM DESKTOP</option>--%>
                                                    <%--                                                        <option value="34">RETURN DESKTOP</option>--%>
                                                    <%--                                                        <option value="8" selected>SUBMITTED</option>--%>
                                                    <%--                                                        <option value="80">INSPECTION FORWARDED</option>--%>
                                                    <%--                                                    </c:if>--%>
                                                    <%--                                                    <c:if test="${ME_PAGE_TYPE eq 2}">--%>
                                                    <%--                                                        <option value="0" selected>ALL</option>--%>
                                                    <%--                                                        <option value="29">PENDING</option>--%>
                                                    <%--                                                        <option value="33">FORWARD FOR INFORM DESKTOP</option>--%>
                                                    <%--                                                        <option value="34">RETURNED DESKTOP INSPECTION</option>--%>
                                                    <%--                                                    </c:if>--%>

                                                </select>
                                                <script>
                                                    if(${ME_PAGE_TYPE eq 50 || ME_PAGE_TYPE eq 60}) {
                                                        $("#txtV_status").val(0);
                                                    }
                                                </script>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtJobNumber" class="col-sm-4 col-form-label"> Job
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtJobNumber" id="txtJobNumber"
                                                       class="form-control form-control-sm" placeholder="Job Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtJobNumber" class="col-sm-4 col-form-label"> Assessment Appr.
                                                Status</label>
                                            <div class="col-sm-8">


                                                <select name="txtAssessmentApprStatus" id="txtAssessmentApprStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="1">Approved</option>
                                                    <option value="2">Pending</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row" id="offerTypeDiv">
                                            <label for="txtJobNumber" class="col-sm-4 col-form-label"> Offer
                                                Type</label>
                                            <div class="col-sm-8">
                                                <select name="txtOfferType" class="form-control form-control-sm  "
                                                        id="txtOfferType">
                                                    <option value="0">Please Select One</option>
                                                    <option value="1">On site Offer</option>
                                                    <option value="2">Garage Offer</option>
                                                    <option value="3">Desktop Offer</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                                <%--<div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> 3<sup>rd</sup>
                                                Party Vehicle Number</label>
                                            <div class="col-sm-8">
                                                <input name="txt3rdVehicleNumber" id="txt3rdVehicleNumber"
                                                        class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                    </div>
                                </div>--%>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <input type="hidden" value="${PENDING_INSPECTION_CLAIM_NO}" name="type" id="pendingInspectionClaimNo">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-priority border">&nbsp;</span> &nbsp;&nbsp;Priority
                                    High
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-danger"></i>&nbsp;&nbsp;Late
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-warning"></i> &nbsp;&nbsp; On
                                    Site</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>

                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-warning"></i>&nbsp;&nbsp;Exceed
                                    24 Hours </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-danger"></i> &nbsp;&nbsp;
                                    Exceed 48 Hours</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th>ref no</th>
                                                <th width="40px">No</th>
                                                <th>Job No</th>
                                                <th>Claim No</th>
                                                <th>Vehicle No</th>
                                                <th>Inspection Type</th>
                                                <c:choose>
                                                    <c:when test="${ME_PAGE_TYPE eq 70}">
                                                        <th>Approve Assigned RTE</th>
                                                        <th>Approve Assigned Date / Time</th>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <th>Assigned RTE</th>
                                                        <th>Assigned Date / Time</th>
                                                    </c:otherwise>
                                                </c:choose>
                                                <th>Job Status</th>
                                                <th>Claim Status</th>
                                                <th>Assessment Appr. Status</th>
                                                <th>Assessor Fee Appr. Status</th>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <c:if test="${successMessage!=null && successMessage!=''}">
        <script type="text/javascript">
            notify('${successMessage}', "success");
        </script>
    </c:if>
    <c:if test="${errorMessage!=null && errorMessage!=''}">
        <script type="text/javascript">
            notify('${errorMessage}', "danger");
        </script>
    </c:if>

</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/assessor-job-datatables.js?v8"></script>
</body>
</html>
