package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class ReminderPrintDto implements Serializable {
    private Integer txnId;
    private Integer claimNo;
    private Integer docTypeId;
    private String printUserId;
    private String printDateTime;
    private String checkReminderPrint;
    private String genaratedDateTime;
    private String recordStatus;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDocTypeId() {
        return docTypeId;
    }

    public void setDocTypeId(Integer docTypeId) {
        this.docTypeId = docTypeId;
    }

    public String getPrintUserId() {
        return printUserId;
    }

    public void setPrintUserId(String printUserId) {
        this.printUserId = printUserId;
    }

    public String getPrintDateTime() {
        return printDateTime;
    }

    public void setPrintDateTime(String printDateTime) {
        this.printDateTime = printDateTime;
    }

    public String getCheckReminderPrint() {
        return checkReminderPrint;
    }

    public void setCheckReminderPrint(String checkReminderPrint) {
        this.checkReminderPrint = checkReminderPrint;
    }

    public String getGenaratedDateTime() {
        return genaratedDateTime;
    }

    public void setGenaratedDateTime(String genaratedDateTime) {
        this.genaratedDateTime = genaratedDateTime;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }
}
