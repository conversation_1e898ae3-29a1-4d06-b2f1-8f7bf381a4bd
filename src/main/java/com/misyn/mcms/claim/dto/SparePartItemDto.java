package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class SparePartItemDto implements Serializable {

    private Integer sparePartRefNo;
    private String sparePartName;
    private String recordStatus = AppConstant.RECORDE_STATUS;
    private String inputUserId;
    private String inputDateTime;
    private Integer index;

    public Integer getSparePartRefNo() {
        return sparePartRefNo;
    }

    public void setSparePartRefNo(Integer sparePartRefNo) {
        this.sparePartRefNo = sparePartRefNo;
    }

    public String getSparePartName() {
        return sparePartName;
    }

    public void setSparePartName(String sparePartName) {
        this.sparePartName = sparePartName;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
