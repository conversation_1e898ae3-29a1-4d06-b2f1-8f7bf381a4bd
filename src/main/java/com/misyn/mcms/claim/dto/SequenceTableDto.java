package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class SequenceTableDto implements Serializable {

    private int tableId = AppConstant.ZERO_INT;
    private String tableName = AppConstant.STRING_EMPTY;
    private int maxKeyValue = AppConstant.ZERO_INT;
    private String maxSysDate = AppConstant.STRING_EMPTY;

    public int getTableId() {
        return tableId;
    }

    public void setTableId(int tableId) {
        this.tableId = tableId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public int getMaxKeyValue() {
        return maxKeyValue;
    }

    public void setMaxKeyValue(int maxKeyValue) {
        this.maxKeyValue = maxKeyValue;
    }

    public String getMaxSysDate() {
        return maxSysDate;
    }

    public void setMaxSysDate(String maxSysDate) {
        this.maxSysDate = maxSysDate;
    }

}
