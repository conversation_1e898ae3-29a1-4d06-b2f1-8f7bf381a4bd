package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;
public class TrailerDetailDto {
    private String trailerNo;
    private BigDecimal sumInsured;
    private BigDecimal autoPremium;
    private BigDecimal manualPremium;
    private String remark;

    public TrailerDetailDto() {
    }

    public TrailerDetailDto(String trailerNo, BigDecimal sumInsured, BigDecimal autoPremium, BigDecimal manualPremium, String remark) {
        this.trailerNo = trailerNo;
        this.sumInsured = sumInsured;
        this.autoPremium = autoPremium;
        this.manualPremium = manualPremium;
        this.remark = remark;
    }

    public String getTrailerNo() {
        return trailerNo;
    }

    public void setTrailerNo(String trailerNo) {
        this.trailerNo = trailerNo;
    }

    public BigDecimal getSumInsured() {
        return sumInsured;
    }

    public void setSumInsured(BigDecimal sumInsured) {
        this.sumInsured = sumInsured;
    }

    public BigDecimal getAutoPremium() {
        return autoPremium;
    }

    public void setAutoPremium(BigDecimal autoPremium) {
        this.autoPremium = autoPremium;
    }

    public BigDecimal getManualPremium() {
        return manualPremium;
    }

    public void setManualPremium(BigDecimal manualPremium) {
        this.manualPremium = manualPremium;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
