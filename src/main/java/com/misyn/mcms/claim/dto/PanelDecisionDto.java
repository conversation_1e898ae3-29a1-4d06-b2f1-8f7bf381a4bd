package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class PanelDecisionDto implements Serializable {
    private String panelDate;
    private String userName;
    private String assignDateTime;
    private String decision;
    private String decisionDateTime;
    private String comment;
    private String dmRemark = AppConstant.STRING_EMPTY;
    private String highlightColor;

    public String getDmRemark() {
        return dmRemark;
    }

    public void setDmRemark(String dmRemark) {
        this.dmRemark = dmRemark;
    }



    public String getPanelDate() {
        return panelDate;
    }

    public void setPanelDate(String panelDate) {
        this.panelDate = panelDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(String assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public String getDecision() {
        return decision;
    }

    public void setDecision(String decision) {
        this.decision = decision;
    }

    public String getDecisionDateTime() {
        return decisionDateTime;
    }

    public void setDecisionDateTime(String decisionDateTime) {
        this.decisionDateTime = decisionDateTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getHighlightColor() {
        return highlightColor;
    }

    public void setHighlightColor(String highlightColor) {
        this.highlightColor = highlightColor;
    }
}
