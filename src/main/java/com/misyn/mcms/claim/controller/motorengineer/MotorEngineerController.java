package com.misyn.mcms.claim.controller.motorengineer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.enums.SupplyOrderStatusEnum;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.exception.WrongValueException;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.AssessorFeeServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Parameters;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.misyn.mcms.utility.AppConstant.*;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "MotorEngineerController", urlPatterns = "/MotorEngineerController/*")
public class MotorEngineerController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorEngineerController.class);
    private int draw = 1;
    private InspectionDetailsService inspectionDetailsService = null;
    private MotorEngineerService motorEngineerService = null;
    private CallCenterService callCenterService = null;
    private AssessorAllocationService assessorAllocationService = null;
    private RequestAriService requestAriService = null;
    private StorageService stDocumentService = null;
    private AssessorPaymentDetailsService assessorPaymentDetailsService = null;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private CalculationSheetService calculationSheetService;
    private SupplyOrderService supplyOrderService;
    private ClaimHandlerService claimHandlerService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private AssessorFeeService assessorFeeService = new AssessorFeeServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        inspectionDetailsService = getInspectionDetailsBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        requestAriService = getByRequestAri(request);
        stDocumentService = getSftpDocumentService(request);
        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
        calculationSheetService = getCalculationSheetServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        assessorPaymentDetailsService = getAssessorPaymentServiceBySession(request);
        supplyOrderService = getSupplyOrderServiceBySession(request);
        ClaimsDto claimsDto = new ClaimsDto();
        Integer claimId = AppConstant.ZERO_INT;
        String historyRecord = AppConstant.NO;
        Integer jobRefNo;

        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        /*if (null != request.getParameter("TYPE") && (null == request.getSession().getAttribute("ME_PAGE_TYPE") || ((String) request.getSession().getAttribute("ME_PAGE_TYPE")).isEmpty())) {
            request.getSession().setAttribute("ME_PAGE_TYPE", request.getParameter("TYPE"));
        }*/
        try {
            switch (pathInfo) {
                case "/jobList":
                    jobList(request, response);
                    break;
                case "/jobView":
                    int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    request.getSession().setAttribute("ME_PAGE_TYPE", request.getParameter("TYPE"));
                    session.setAttribute(AppConstant.SESSION_TYPE, type);
                    if (30 == type) {
                        request.setAttribute("PENDING_INSPECTION_CLAIM_NO", request.getParameter(AppConstant.CLAIM_NO));
                    }
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionList.jsp");
                    break;
                case "/viewEdit":
                    viewEdit(request, response, 0, false);
                    break;
                case "/viewClaimHistory":
//                    removeSessionClaimDetails(request, response);
                    claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = callCenterService.getViewAccidentClaimsDto(claimId);
                    //  claimsDto.setAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getAccidTime()));
                    //   claimsDto.setReportAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getTimeOfReport()));

                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);

                    //request.setAttribute(AppConstant.HIDE_ALLOCATION, AppConstant.YES);

                    session.removeAttribute(AppConstant.CLAIM_DTO_HISTORY);
                    session.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/viewClaimHistoryBillCheck":
//                    removeSessionClaimDetails(request, response);
                    claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = callCenterService.getViewAccidentClaimsDto(claimId);
                    //  claimsDto.setAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getAccidTime()));
                    //   claimsDto.setReportAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getTimeOfReport()));

                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);

//                    request.setAttribute(AppConstant.HIDE_ALLOCATION, AppConstant.YES);

                    session.removeAttribute(AppConstant.CLAIM_DTO_HISTORY);
                    session.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/approve":
                    approveDetails(request, response);
                    break;
                case "/requestARI":
                    Integer claimNo = getMotorEngineerDetailsDto(request).getClaimNo();
                    List<RequestAriDto> list = requestAriService.searchAll(claimNo);
                    request.setAttribute(AppConstant.REQUESTED_ARI_LIST, list);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/requestARI.jsp");
                    break;
                case "/thirdPartyDetails":
                    InspectionDetailsDto inspectionDetailsDtoTP = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
                    request.setAttribute("inspectionDetailsDto", inspectionDetailsDtoTP);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/thirdPartyDetails.jsp");
                    break;
                case "/saveAri":
                    saveAri(request, response);
                    break;
                case "/calculateProfessionalFee":
                    calculateProfessionalFee(request, response);
                    break;
                case "/documentUpload":
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    jobRefNo = request.getParameter("JOB_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("JOB_REF_NO"));
                    MotorEngineerDetailsDto motorEngineerDetailsDto;
                    InspectionDetailsDto inspectionDetailsDto = this.getInspectionDetailsDto(request, response, historyRecord);
                    boolean approveStatus = false;
                    approveStatus = inspectionDetailsService.isRteApproved(jobRefNo);

                    session.removeAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST);
                    if (inspectionDetailsDto != null) {
                        Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                        List<ClaimUploadViewDto> claimUploadViewDtoList = inspectionDetailsService.getClaimUploadViewDtoList(inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionTypeId);
                        session.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
                    }
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    request.setAttribute(AppConstant.APPROVE_STATUS, approveStatus);
                    request.setAttribute(AppConstant.JOB_REF_NO, jobRefNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/documentUpload.jsp");
                    break;
                case "/processThirdPartyDetails":
                    processThirdPartyDetails(request, response);
                    break;
                case "/documentViewer":
                    /* Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
                    Integer jobRefNo = Integer.parseInt(request.getParameter(AppConstant.JOB_REF_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.JOB_REF_NO));
                    List<ClaimDocumentDto> claimDocumentDtoList = inspectionDetailsService.getClaimDocumentDtoList(jobRefNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
                    session.setAttribute(AppConstant.SESSION_CLAIM_DOCUMENT_DTO_LIST, claimDocumentDtoList);
                    request.setAttribute("refNo", refNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/pdfViewerAll.jsp");
                     */
                    Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
                    jobRefNo = Integer.parseInt(request.getParameter(AppConstant.JOB_REF_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.JOB_REF_NO));
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    List<ClaimDocumentDto> claimDocumentDtoList = inspectionDetailsService.getClaimDocumentDtoList(jobRefNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
                    if (historyRecord.equals(AppConstant.YES)) {
                        request.setAttribute(AppConstant.HISTORY_CLAIM_DOCUMENT_DTO_LIST, claimDocumentDtoList);
                    } else {
                        session.setAttribute(AppConstant.SESSION_CLAIM_DOCUMENT_DTO_LIST, claimDocumentDtoList);
                    }
                    request.setAttribute("refNo", refNo);
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/pdfViewerAll.jsp");
                    break;
                case "/viewEditPrevious":
                    viewEditPrevious(request, response);
                    break;
                case "/imageUpload":
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION");
                    inspectionDetailsDto = this.getInspectionDetailsDto(request, response, historyRecord);
                    session.removeAttribute(AppConstant.SESSION_CLAIM_IMAGE_DTO_LIST);
                    jobRefNo = request.getParameter("JOB_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("JOB_REF_NO"));
                    approveStatus = false;
                    approveStatus = inspectionDetailsService.isRteApproved(jobRefNo);
                    if (inspectionDetailsDto != null) {
                        //  Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                        List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(inspectionDetailsDto.getClaimNo(), jobRefNo);
                        session.setAttribute(AppConstant.SESSION_CLAIM_IMAGE_DTO_LIST, claimImageDtoList);
                    }
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    request.setAttribute(AppConstant.APPROVE_STATUS, approveStatus);
                    request.setAttribute(AppConstant.JOB_REF_NO, jobRefNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/imageUpload.jsp");
                    break;
                case "/imageViewer":
                    motorEngineerDetailsDto = getMotorEngineerDetailsDto(request);
                    inspectionDetailsDto = motorEngineerDetailsDto.getInspectionDetailsDto();
                    refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
                    jobRefNo = inspectionDetailsDto.getRefNo();
                    List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(inspectionDetailsDto.getClaimNo(), jobRefNo);
                    session.setAttribute(AppConstant.SESSION_VIEWER_CLAIM_IMAGE_LIST, claimImageDtoList);
                    request.setAttribute("refNo", refNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/imageViewer.jsp");
                    break;
                case "/deleteImages":
                    deleteImages(request, response);
                    break;
                case "/deleteDocs":
                    docDeltete(request, response);
                    break;
                case "/calculateOnsiteInspectionValues":
                    calculateOnsiteInspectionValues(request, response);
                    break;
                case "/calculateUnderInsPenaltyPerc":
                    calculateUnderInsPenaltyPerc(request, response);
                    break;
                case "/forwardToInformDesktop":
                    forwardToInformDesktop(request, response);
                    break;
                case "/informDesktop":
                    informDesktop(request, response);
                    break;
                case "/recallDesktop":
                    recallDesktop(request, response);
                    break;
                case "/taskReturnDesktop":
                    taskReturnDesktop(request, response);
                    break;
                case "/getTcUserList":
                    getTcUserList(request, response);
                    break;
                case "/returnDesktop":
                    returnDesktop(request, response);
                    break;
                case "/viewEditClaimPrevious":
                    viewEditClaimPrevious(request, response);
                    break;
                case "/updateChangeRequest":
                    updateChangeRequest(request, response);
                    break;
                case "/updateAsChangeRequest":
                    updateAsChangeRequest(request, response);
                    break;
                case "/addRteSpecialRemarks":
                    addRteSpecialRemarks(request, response);
                    break;
                case "/viewLogDetails":
                    MotorEngineerDetailsDto engineerDetailsDto = new MotorEngineerDetailsDto();
                    Integer jobRefNos = request.getParameter("jobRefNo") == null ? 0 : Integer.parseInt(request.getParameter("jobRefNo"));
                    Integer claimNos = null == request.getParameter(AppConstant.P_N_CLIM_NO) ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
                    List<ClaimLogTrailDto> loggerTrailList = motorEngineerService.getLogDetails(claimNos, jobRefNos);
                    if (null != loggerTrailList && !loggerTrailList.isEmpty()) {
                        engineerDetailsDto.setLogList(loggerTrailList);
                    }
                    request.setAttribute(AppConstant.LOG_DETAILS, engineerDetailsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/logDetails.jsp");
                    break;
                case "/viewSupplyOrderCheck":
                    viewSupplyOrderCheck(request, response);
                    break;
                case "/viewBillCheck":
                    viewBillCheck(request, response);
                    break;
                case "/viewDocumentUpload":
                    viewDocumentUpload(request, response);
                    break;
                case "/viewBillUpload":
                    viewBillUpload(request, response);
                    break;
                case "/viewBillImage":
                    viewBillImageUpload(request, response);
                    break;
                case "/updateBillCheckComplete":
                    updateBillCheckComplete(request, response);
                    break;
                case "/returnToClaimHandlerBySpecialTeam":
                    returnToClaimHandlerBySpecialTeam(request, response);
                    break;
                case "/returnToSparePartCoordinatorByScrutinizingTeam":
                    returnToSparePartCoordinatorByScrutinizingTeam(request, response);
                    break;
                case "/paymentHold":
                    paymentHold(request, response);
                    break;
                case "/isApproveAcrAndIsOnsitePending":
                    isApproveAcrAndIsOnsitePending(request, response);
                    break;
                case "/setBalanceAdvanceAmount":
                    setBalanceAdvanceAmount(request, response);
                    break;
                case "/forwardToRte":
                    forwardToRte(request, response);
                    break;
                case "/getRteList":
                    getRteList(request, response);
                    break;
                case "/isForward":
                    isForward(request, response);
                    break;
                case "/returnByApproveAssignRte":
                    returnByApproveAssignRte(request, response);
                    break;
                case "/viewReserveCheck":
                    viewReserveCheck(request, response);
                    break;
                case "/getReportingRteList":
                    getReportingRteList(request, response);
                    break;
                case "/viewEngDocUpload":
                    viewEngDocUpload(request, response);
                    break;
                case "/recallFromForwardedEngineer":
                    recallFromForwardedEngineer(request, response);
                    break;
                case "/returnToClaimHandlerBySpc":
                    returnToClaimHandlerBySpc(request, response);
                    break;
                case "/returnToClaimHandler":
                    returnToClaimHandler(request, response);
                    break;
                case "/sparePartsCoordRequestAriAndReturn":
                    sparePartsCoordRequestAriAndReturn(request, response);
                    break;
                case "/scrutinizingRequestAriAndReturn":
                    scrutinizingRequestAriAndReturn(request, response);
                    break;
                case "/returnDoToClaimHandler":
                    returnDoToClaimHandler(request, response);
                    break;
                case "/callOnMiSiteOnlineAssessment":
                    getOnMiSiteOnlineAssessmentData(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void returnDoToClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null || request.getParameter("claimNo").isEmpty() ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer reason = null == request.getParameter("V_REASON") || request.getParameter("V_REASON").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("V_REASON"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        try {
            boolean isRequested = claimHandlerService.requestedAri(claimNo, reason, remark, user);
            if (isRequested) {
                SupplyOrderSummaryDto activeSupplyOrderDetails = supplyOrderService.getActiveSupplyOrderDetails(claimNo);
                if (null != activeSupplyOrderDetails && activeSupplyOrderDetails.getSupplyOrderStatus().equals(SupplyOrderStatusEnum.FORWARD_SCRUTINIZING_TEAM.getSupplyOrderStatusEnum())) {
                    supplyOrderService.returnToSparePartsCoordinator(claimNo, supplyOrderRefNo, user);
                } else {
                    supplyOrderService.returnToClaimHandler(claimNo, supplyOrderRefNo, user, true);
                }
                successMessage = "Successfully saved";
            } else {
                errorMessage = "Return Failed";
            }
        } catch (ErrorMsgException e) {
            errorMessage = e.getErrorMessage();
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewSupplyOrderCheck(request, response);
        }
    }

    private void scrutinizingRequestAriAndReturn(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO").isEmpty() ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        Integer reason = null == request.getParameter("V_REASON") || request.getParameter("V_REASON").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("V_REASON"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        try {
            boolean isRequested = claimHandlerService.requestedAri(claimNo, reason, remark, user);
            if (isRequested) {
                calculationSheetService.returnToClaimHandlerByScrutinizingTeam(calSheetId, claimNo, user, true);
                successMessage = "Successfully saved";
            } else {
                successMessage = "Return Failed";
            }
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }

    private void sparePartsCoordRequestAriAndReturn(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO").isEmpty() ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        Integer reason = null == request.getParameter("V_REASON") || request.getParameter("V_REASON").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("V_REASON"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        try {
            boolean isRequested = claimHandlerService.requestedAri(claimNo, reason, remark, user);
            if (isRequested) {
                calculationSheetService.returnToClaimHandlerBySpc(calSheetId, claimNo, user, true);
                successMessage = "Successfully saved";
            } else {
                successMessage = "Return Failed";
            }
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }

    private void recallFromForwardedEngineer(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        String json;
        Gson gson = new Gson();
        boolean recall = false;
        UserDto user = getSessionUser(request);
        try {
            recall = claimHandlerService.recallFromForwardedEngineer(claimNo, user, remark);
            if (recall) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void viewEngDocUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            String prevInspection = request.getParameter(AppConstant.PREVIOUS_INSPECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PREVIOUS_INSPECTION);
            if (AppConstant.YES.equals(prevInspection)) {
                request.setAttribute(AppConstant.PREVIOUS_INSPECTION, prevInspection);
            }
            claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, "'ENGINEERING_DOC'");
            request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            request.setAttribute(AppConstant.PENDING_INSPECTION, false);
            request.setAttribute("ENG_DOC", true);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/billDocumentUploadView.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getReportingRteList(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            List<UserDto> reportingRteList = assessorAllocationService.getRTEList();
            json = gson.toJson(reportingRteList);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void viewReserveCheck(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        Integer calSheetNo = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_TAB_INDEX));
        try {
            int type = request.getParameter(AppConstant.TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.TYPE));
            if (type > 0) {
                updateSessionType(request, response, type);
            }
        } catch (Exception e) {
        }
        boolean pendingInspection = false;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        List<MotorEngineerDetailsDto> motorEngineerDetailsList = null;
        List<PreviousClaimsDto> previousClaimList = null;
        List<PreviousClaimsDto> previousInspectionList = null;
        List<SpecialRemarkDto> specialRemarkDtos = null;
        UserDto user = getSessionUser(request);
        ClaimHandlerDto claimHandlerDto = null;
        try {
            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            motorEngineerDetailsList = motorEngineerService.getMotorEngineerDetailsDtoList(claimNo);
            if (!motorEngineerDetailsList.isEmpty()) {
                motorEngineerDetailsDto = motorEngineerDetailsList.get(0);
            }

            previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, 0);

            claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }
            pendingInspection = motorEngineerService.checkPendingInspection(claimNo);
            specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS_LIST, motorEngineerDetailsList);
            request.setAttribute(AppConstant.TAB_INDEX, tabIndex);
            request.getSession().setAttribute(AppConstant.CALCULATION_SHEET_NO, calSheetNo);
            request.getSession().setAttribute(AppConstant.CLAIM_CALCULATION_SHEET_MAIN_DTO, claimCalculationSheetMainDto);
            request.getSession().setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);

            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/reserveCheckInspectionDetails.jsp");
        }
    }

    private void returnByApproveAssignRte(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String specialRemark = null == request.getParameter("rteSpecialRemark") ? AppConstant.STRING_EMPTY : request.getParameter("rteSpecialRemark");
        String dispatchUrl;
        try {
            motorEngineerDetailsDto.setInspectionSpecialRemark(specialRemark);
            boolean isReturn = motorEngineerService.returnByApproveAssignRte(motorEngineerDetailsDto, user);

            if (isReturn) {
                response.sendRedirect(request.getContextPath() + "/MotorEngineerController" +
                        "/viewEdit?P_N_REF_NO=" + motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo() + "&successCode=" + 4);
                return;
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            request.setAttribute(AppConstant.ERROR_MESSAGE, " Record Can not be Returned");
        }
        dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);

    }

    private void getRteList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();
            List<UserDto> list = assessorAllocationService.getRTEList();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            ;
        }
    }

    private void forwardToRte(HttpServletRequest request, HttpServletResponse response) {
        String json;
        String rteUser = null == request.getParameter("V_ASSIGN_RTE") ? AppConstant.STRING_EMPTY : request.getParameter("V_ASSIGN_RTE");
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        Gson gson = new Gson();
        UserDto user = getSessionUser(request);
        try {
            boolean forwardRte = claimHandlerService.forwardToEngineer(claimNo, rteUser, user, remark);
            if (forwardRte) {
                json = "SUCCESS";
                json = gson.toJson(json);
                printWriter(request, response, json);
            } else {
                json = "FAIL";
                json = gson.toJson(json);
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = "ERROR";
            json = gson.toJson(json);
            printWriter(request, response, json);
        }
    }

    private void setBalanceAdvanceAmount(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        try {
            String calType = request.getParameter("calType");
            BigDecimal balanceAdvanceAmount = new BigDecimal(null == request.getParameter("balanceAdvanceAmount") ? AppConstant.ZERO : request.getParameter("balanceAdvanceAmount"));
            BigDecimal advanceAmountChange = new BigDecimal(null == request.getParameter("advanceAmountChange")
                    || AppConstant.STRING_EMPTY.equals(request.getParameter("advanceAmountChange")) ? AppConstant.ZERO : request.getParameter("advanceAmountChange"));
            BigDecimal newBalanceAdvanceAmount;
            if (AppConstant.ADD.equalsIgnoreCase(calType)) {
                newBalanceAdvanceAmount = balanceAdvanceAmount.add(advanceAmountChange);
            } else {
                newBalanceAdvanceAmount = balanceAdvanceAmount.subtract(advanceAmountChange);
            }
            if (BigDecimal.ZERO.compareTo(newBalanceAdvanceAmount) > 0) {
                json = "ERROR";
            } else {
                json = newBalanceAdvanceAmount.toString();
            }
            json = gson.toJson(json);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void isApproveAcrAndIsOnsitePending(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        BigDecimal acr = new BigDecimal(null == request.getParameter("acr") || request.getParameter("acr").isEmpty() ? AppConstant.ZERO : request.getParameter("acr"));
        BigDecimal sumInsuredVal = new BigDecimal(null == request.getParameter("sumInsuredVal") || request.getParameter("sumInsuredVal").isEmpty() ? AppConstant.ZERO : request.getParameter("sumInsuredVal"));
        BigDecimal pav = new BigDecimal(null == request.getParameter("pav") || request.getParameter("pav").isEmpty() ? AppConstant.ZERO : request.getParameter("pav"));
        BigDecimal totalApprovedAcr = new BigDecimal(null == request.getParameter("totalApprovedAcr") || request.getParameter("totalApprovedAcr").isEmpty() ? AppConstant.ZERO : request.getParameter("totalApprovedAcr"));
        Integer inspectionType = Integer.parseInt(null == request.getParameter("inspectionType") || request.getParameter("inspectionType").isEmpty() ? AppConstant.ZERO : request.getParameter("inspectionType"));
        String covn = null == request.getParameter("covn") ? AppConstant.STRING_EMPTY : request.getParameter("covn");
        String claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO : request.getParameter("claimNo");
        try {
            boolean isAcrCanApprove;
            if ("COVN".equalsIgnoreCase(covn)) {
                isAcrCanApprove = true;
            } else {
                isAcrCanApprove = motorEngineerService.isAcrCanBeApprove(inspectionType, acr, sumInsuredVal, pav, totalApprovedAcr, Integer.parseInt(claimNo));
            }
            if (isAcrCanApprove) {
                json = AppConstant.SUCCESS_MESSAGE;
            }
            if ((AppConstant.GARAGE_INSPECTION == inspectionType || AppConstant.DESKTOP_INSPECTION == inspectionType) && motorEngineerService.isOnsiteOrOffSitePending(Integer.parseInt(claimNo))) {
                json = "IS_ONSITE_PENDING";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);
        } catch (ErrorMsgException e) {
            json = "APPROVE_ACR_FAIL";
            json = gson.toJson(json);
            printWriter(request, response, json);
        }
    }

    private void updateBillCheckComplete(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        UserDto user = getSessionUser(request);
        try {
            calculationSheetService.returnToClaimHandlerByStm(calSheetId, claimNo, user);
            successMessage = "Bill check successfully completed";
        } catch (Exception e) {
            errorMessage = "Bill check failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }


    private void returnToClaimHandlerBySpecialTeam(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        UserDto user = getSessionUser(request);

        try {
            calculationSheetService.returnToClaimHandlerByScrutinizingTeam(calSheetId, claimNo, user, false);
            successMessage = "Successfully Returned";
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }

    private void returnToClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.returnToClaimHandler(claimNo, supplyOrderRefNo, user, false);
            successMessage = "Successfully Returned";
        } catch (ErrorMsgException e) {
            errorMessage = e.getErrorMessage();
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewSupplyOrderCheck(request, response);
        }
    }

    private void returnToClaimHandlerBySpc(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return To Claim Handler By Spare Part Coordinator ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.returnToClaimHandlerBySpc(calSheetId, claimNo, user, false);
            successMessage = "Successfully Returned";
        } catch (Exception e) {
            errorMessage = "Return failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }

    private void returnToSparePartCoordinatorByScrutinizingTeam(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        Integer calSheetId = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        UserDto user = getSessionUser(request);

        try {
            calculationSheetService.returnToSparePartCoordinatorByScrutinizingTeam(calSheetId, claimNo, user);
            successMessage = "Successfully saved";
        } catch (UserNotFoundException e) {
            errorMessage = e.getErrorMessage();
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
            errorMessage = "Save failed";
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            viewBillCheck(request, response);
        }
    }

    private void paymentHold(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String dispatchUrl;
        ContactDetailDto contactDetailDto = null;
        try {
            String paymentStatus = assessorPaymentDetailsService.getPaymentStatus(motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
            if (paymentStatus.equalsIgnoreCase(AppConstant.HOLD)) {
                throw new WrongValueException(AppConstant.ERROR_MESSAGE, "Assessor Payment Already Held");
            } else if (paymentStatus.equalsIgnoreCase(AppConstant.REJECT)) {
                throw new WrongValueException(AppConstant.ERROR_MESSAGE, "Assessor Payment Already Rejected");
            }
            assessorPaymentDetailsService.updateHoldPayment(motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo(), user, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getInspectionDetailsDto().getAssessorAllocationDto().getAssessorDto().getCode());
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Assessor Payment Hold Successfully");
            contactDetailDto = motorEngineerService.getContactDetailForRte(motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser());
            request.setAttribute(AppConstant.CONTACT_DETAIL_DTO, contactDetailDto);
            request.setAttribute(AppConstant.PAYMENT_STATUS, paymentStatus);
            motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorFeeAuthStatus(AppConstant.HOLD);
        } catch (WrongValueException e) {
            LOGGER.error(e.getMessage(), e);
            request.setAttribute(AppConstant.ERROR_MESSAGE, e.getErrorMessage());
            motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(motorEngineerDetailsDto.getRecordStatus());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Assessor Payment Can not be Hold");
            motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(motorEngineerDetailsDto.getRecordStatus());
        }
        dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
        request.setAttribute(AppConstant.ACTION, AppConstant.UPDATE);
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);

    }


    private void viewBillImageUpload(HttpServletRequest request, HttpServletResponse response) {
        Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
        List<ClaimImageFormDto> claimImageFormDtos = inspectionDetailsService.getClaimImageFormDtoList(claimId);
        request.setAttribute(AppConstant.CLAIM_IMAGE_FORM_DTO_LIST, claimImageFormDtos);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/imageUpload.jsp");
    }

    private void addRteSpecialRemarks(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        boolean updated = false;

        try {
            MotorEngineerDetailsDto motorEngineerDetailsDto = new MotorEngineerDetailsDto();
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            String remark = request.getParameter("remark");
            Integer claimNo = request.getParameter("claimNo") == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            beanUtilsBean.getConvertUtils().register(false, false, 0);
            beanUtilsBean.populate(motorEngineerDetailsDto, request.getParameterMap());
            if (remark != null) {
                motorEngineerDetailsDto.setInspectionSpecialRemark(remark);
                motorEngineerDetailsDto.setClaimNo(claimNo);
            }
            MotorEngineerDetailsDto sessionMotorEng = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
            if (null != sessionMotorEng && null != sessionMotorEng.getInspectionDto()) {
                motorEngineerDetailsDto.setInspectionDto(sessionMotorEng.getInspectionDto());
            }

            if (null != claimNo || claimNo != 0) {
                updated = motorEngineerService.addSpecialRemark(motorEngineerDetailsDto, user);
            }
            if (updated) {
                json = "Successfully Add Special Remark";
            } else {
                json = "Fail to Add Special Remark";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void jobList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String jobNo = request.getParameter(AppConstant.TXT_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String inspectionId = request.getParameter(AppConstant.TXT_INSPECTION_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);
        String approveStatus = request.getParameter(AppConstant.TXT_ASSESEMENT_APPR_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSESEMENT_APPR_STATUS);
        String assessorFeeStatus = request.getParameter(AppConstant.TXT_ASSESEOR_APPR_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSESEOR_APPR_STATUS);
        String offerType = request.getParameter(AppConstant.TXT_OFFER_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_OFFER_TYPE);

        String isRteOrAssessorDetails = AppConstant.STRING_EMPTY;

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t2.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t2.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t2.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t2.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t2.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t2.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.job_id", jobNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.job_status", "23,4,29", FieldParameterDto.SearchType.NOT_IN, parameterList);
            if (!"0".equals(inspectionId)) {
                this.addFieldParameter("t1.insepction_id ", inspectionId, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (!"".equals(approveStatus)) {
                if ("1".equals(approveStatus)) {
                    this.addFieldParameter("t0.v_ass_esti_apr_status", "'A'", FieldParameterDto.SearchType.IN, parameterList);
                }

                if ("2".equals(approveStatus)) {
                    this.addFieldParameter("t0.v_ass_esti_apr_status", "'P'", FieldParameterDto.SearchType.IN, parameterList);
                }

            }

            if (!"".equals(assessorFeeStatus)) {
                if ("1".equals(assessorFeeStatus)) {
                    this.addFieldParameter("t0.v_ass_fee_apr_status", "'A'", FieldParameterDto.SearchType.IN, parameterList);
                }

                if ("2".equals(assessorFeeStatus)) {
                    this.addFieldParameter("t0.v_ass_fee_apr_status", "'P'", FieldParameterDto.SearchType.IN, parameterList);
                }

            }


//            if (user.getAccessUserType() == 6) {
//                this.addFieldParameter("t3.assessor_code", empNo, FieldParameterDto.SearchType.Equal, parameterList);
//
//            }
            if (!"0".equals(status)) {
                this.addFieldParameter("t1.record_status", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if ("9".equals(status) || "10".equals(status)) {
                isRteOrAssessorDetails = AppConstant.RTE_DETAILS;
            } else if ("8".equals(status)) {
                isRteOrAssessorDetails = AppConstant.ASSESSOR_DETAILS;
            }

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(isRteOrAssessorDetails))
                if ("1".equals(offerType)) {
                    this.addFieldParameter("t5.offer_type", "1", FieldParameterDto.SearchType.Equal, parameterList);
                } else if ("2".equals(offerType)) {
                    this.addFieldParameter("t5.settlement_method", "5", FieldParameterDto.SearchType.Equal, parameterList);
                } else if ("3".equals(offerType)) {
                    this.addFieldParameter("t5.desktop_offer", "Y", FieldParameterDto.SearchType.Equal, parameterList);
                }

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = " t1.ref_no";
                    break;
                case "claimNo":
                    orderColumnName = "t2.N_CLIM_NO";
                    break;
                case "policyNumber":
                    orderColumnName = "t2.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t2.V_VEHICLE_NO";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t2.V_COVER_NOTE_NO";
                    break;
                case "statusId":
                    orderColumnName = "t1.record_status";
                    break;
                case "jobNo":
                    orderColumnName = "t1.job_id";
                    break;
                case "inspectionType":
                    orderColumnName = "t3.inspection_type_id";
                    break;
                case "assignToRteDateTime":
                    orderColumnName = "t0.d_inpdatetime";
                    break;
                case "jobStatus":
                    orderColumnName = "t1.job_status";
                    break;
                case "estimationApprStatus":
                    orderColumnName = "t0.v_ass_esti_apr_status";
                    break;
                case "assFeeAprStatus":
                    orderColumnName = "t0.v_ass_fee_apr_status";
                    break;
                case "assignUser":
                    orderColumnName = "t0.v_assign_rte_user";
                    break;
            }
            String type = (String) request.getSession().getAttribute("ME_PAGE_TYPE");
            DataGridDto data = new DataGridDto();
            if ("1".equals(type) || "30".equals(type) || "70".equals(type)) {
                data = inspectionDetailsService.getSubmittedInspectionOfferDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, Integer.parseInt(offerType), isRteOrAssessorDetails, type, !inspectionId.isEmpty() ? inspectionId : AppConstant.ZERO);
            } else if ("2".equals(type)) {
                data = inspectionDetailsService.getFwdDesktopInspectionDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, status.isEmpty() ? 0 : Integer.parseInt(status));
            } else if ("50".equals(type)) {
                data = inspectionDetailsService.getSparePartsCoordInspectionDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, Integer.valueOf(offerType), status.isEmpty() ? 0 : Integer.parseInt(status));
            } else if ("60".equals(type)) {
                data = inspectionDetailsService.getScrutinizingInspectionDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, Integer.valueOf(offerType), status.isEmpty() ? 0 : Integer.parseInt(status));
            }

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewSupplyOrderCheck(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
//        Integer calSheetNo = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_TAB_INDEX));
        boolean isDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
        Integer refNo = null == request.getParameter("N_REF_NO") || request.getParameter("N_REF_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_REF_NO"));
        try {
            Integer type = request.getParameter(AppConstant.TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.TYPE));
            if (type > 0) {
                updateSessionType(request, response, type);
            }
        } catch (Exception e) {
        }

        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        //ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        List<MotorEngineerDetailsDto> motorEngineerDetailsList = null;
        List<PreviousClaimsDto> previousClaimList = null;
        List<PreviousClaimsDto> previousInspectionList = null;
        List<SpecialRemarkDto> specialRemarkDtos = null;

        ClaimHandlerDto claimHandlerDto = null;
        boolean pendingInspection = false;
        try {
            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            motorEngineerDetailsList = motorEngineerService.getMotorEngineerDetailsDtoList(claimNo);
            if (!motorEngineerDetailsList.isEmpty()) {
                motorEngineerDetailsDto = motorEngineerDetailsList.get(0);
            }

            if (null != claimsDto.getVehicleNo() && !claimsDto.getVehicleNo().isEmpty()) {
                previousClaimList = inspectionDetailsService.getPreviousClaimList(claimsDto.getVehicleNo(), claimNo);
            }
            previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, 0);

            //claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }
            pendingInspection = motorEngineerService.checkPendingInspection(claimNo);
            specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            request.getSession().setAttribute(AppConstant.CLAIMS_DTO, claimsDto);
            Integer supplyOrderRefNo = supplyOrderService.getMaxSupplyRefNo(claimNo);
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderService.getSupplyOrderSummaryDto(supplyOrderRefNo, claimNo);
            UserDto userDetailByUserId = motorEngineerService.getUserDetailByUserId(supplyOrderSummaryDto.getInputUserId());
            BigDecimal authLimit = BigDecimal.valueOf(userDetailByUserId.getPaymentAuthLimit());
            BigDecimal totalAmount = supplyOrderSummaryDto.getTotalAmount();
            if(authLimit.compareTo(totalAmount) < 0){
                supplyOrderSummaryDto.setLimitExceeded(Boolean.TRUE);
            }
            request.setAttribute(AppConstant.SUPPLY_ORDER_SUMMARY_DTO, supplyOrderSummaryDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS_LIST, motorEngineerDetailsList);
            request.setAttribute(AppConstant.TAB_INDEX, tabIndex);
            request.setAttribute(AppConstant.DOC_UPLOAD, isDocUpload);
            request.setAttribute("DO_REF_NO", refNo);
            request.setAttribute("hello", refNo);
            //request.getSession().setAttribute(AppConstant.CALCULATION_SHEET_NO, calSheetNo);
//            session rfemove
            request.getSession().removeAttribute(AppConstant.CLAIM_CALCULATION_SHEET_MAIN_DTO);
            request.getSession().setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/billCheckInspectionDetails.jsp");
        }
    }

    private void viewBillCheck(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        Integer calSheetNo = request.getParameter("P_CAL_SHEET_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_NO"));
        Integer tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_TAB_INDEX));
        try {
            Integer type = request.getParameter(AppConstant.TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.TYPE));
            if (type > 0) {
                updateSessionType(request, response, type);
            }
        } catch (Exception e) {
        }
        boolean pendingInspection = false;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        List<MotorEngineerDetailsDto> motorEngineerDetailsList = null;
        List<PreviousClaimsDto> previousClaimList = null;
        List<PreviousClaimsDto> previousInspectionList = null;
        List<SpecialRemarkDto> specialRemarkDtos = null;
        UserDto user = getSessionUser(request);
        ClaimHandlerDto claimHandlerDto = null;
        try {
            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            motorEngineerDetailsList = motorEngineerService.getMotorEngineerDetailsDtoList(claimNo);
            if (!motorEngineerDetailsList.isEmpty()) {
                motorEngineerDetailsDto = motorEngineerDetailsList.get(0);
            }

            if (null != claimsDto.getVehicleNo() && !claimsDto.getVehicleNo().isEmpty()) {
                previousClaimList = inspectionDetailsService.getPreviousClaimList(claimsDto.getVehicleNo(), claimNo);
            }
            previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, 0);

            if (AppConstant.ZERO_INT == calSheetNo) {
                calSheetNo = calculationSheetService.getCalsheetForBillChecking(claimNo);
            }

            claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }
            pendingInspection = motorEngineerService.checkPendingInspection(claimNo);
            specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            Integer supplyOrderRefNo = supplyOrderService.getMaxSupplyRefNo(claimNo);
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderService.getSupplyOrderSummaryDto(supplyOrderRefNo, claimNo);
            request.setAttribute(AppConstant.SUPPLY_ORDER_SUMMARY_DTO, supplyOrderSummaryDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS_LIST, motorEngineerDetailsList);
            request.setAttribute(AppConstant.TAB_INDEX, tabIndex);
            request.setAttribute(AppConstant.DOC_UPLOAD, false);
            request.setAttribute("DO_REF_NO", AppConstant.ZERO_INT);
            request.getSession().setAttribute(AppConstant.CALCULATION_SHEET_NO, calSheetNo);
            request.getSession().setAttribute(AppConstant.CLAIM_CALCULATION_SHEET_MAIN_DTO, claimCalculationSheetMainDto);
            request.getSession().setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);

            //  request.getSession().setAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            //  request.setAttribute(AppConstant.SUCCESS_MESSAGE, AppConstant.STRING_EMPTY);
            //  request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/billCheckInspectionDetails.jsp");
        }
    }

    private void viewEdit(HttpServletRequest request, HttpServletResponse response, Integer desktopInspectionRefNo, boolean isSavedDesktop) {
        InspectionDetailsDto inspectionDetailsDto = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        ContactDetailDto contactDetailDto = null;
        UserDto user = getSessionUser(request);
        ClaimsDto claimsDto = null;
        try {
            Integer refNo = request.getParameter("P_N_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_REF_NO"));
            if (desktopInspectionRefNo != 0) {
                refNo = desktopInspectionRefNo;
            }

            Integer successCode = null == request.getParameter(AppConstant.SUCCESS_CODE) ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.SUCCESS_CODE));

            if (1 == successCode) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully saved");
            } else if (2 == successCode) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully approved");
            } else if (3 == successCode) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully forwarded");
            } else if (4 == successCode) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully returned");
            }

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            List<ListItemDto> inspectionTimeList = assessorFeeService.getTimeSlotsByInspectionTypeId(inspectionDetailsDto.getInspectionDto().getInspectionId(), inspectionDetailsDto.getJobType());
            List<ListItemDto> dayTypeDtoList = assessorFeeService.getDayTypeList();

            request.setAttribute(AppConstant.DAY_TYPE_LIST, dayTypeDtoList);
            request.setAttribute(AppConstant.INSPECTION_TIME_LIST, inspectionTimeList);

            Integer inspectionType = inspectionDetailsService.getInspectionType(refNo, motorEngineerDetailsDto.getClaimNo());
            Integer prevOnOrOffSiteRefNo = 0;
            if (AppConstant.DESKTOP_INSPECTION == inspectionType || AppConstant.DESKTOP_INSPECTION_ONLINE == inspectionType) {
                prevOnOrOffSiteRefNo = inspectionDetailsService.getRefNoForOnOrOffSite(motorEngineerDetailsDto.getClaimNo());
                claimsDto = callCenterService.getViewAccidentClaimsDto(motorEngineerDetailsDto.getClaimNo());
                request.setAttribute("claimsDto", claimsDto);
            }
            request.setAttribute(AppConstant.PREV_ON_OR_OFF_SITE_REF_NO, prevOnOrOffSiteRefNo);
            AssessorAllocationDto assessorAllocationDto = assessorAllocationService.search(refNo);
            Integer claimNo = assessorAllocationDto.getClaimsDto().getClaimNo();


//            if (null == inspectionDetailsDto && 8 == assessorAllocationDto.getInspectionDto().getInspectionId()) {
//                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
//                motorEngineerDetailsDto.setClaimNo(claimNo);
//                motorEngineerDetailsDto.setRefNo(refNo);
//                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
//                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//                populateInitTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
//
//                motorEngineerDetailsDto.getInspectionDetailsDto().setInputDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//                motorEngineerDetailsDto.getInspectionDetailsDto().setInputUserId(user.getUserId());
//
//                motorEngineerDetailsDto.getInspectionDetailsDto().setAssignRteUser(user.getUserId());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setAssignRteDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//
//                //Set Nested Objects Values To Most Outer Object
//                motorEngineerDetailsDto.setRefNo(motorEngineerDetailsDto.getRefNo());
//                motorEngineerDetailsDto.setJobId(motorEngineerDetailsDto.getJobId());
//                motorEngineerDetailsDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
//
//                motorEngineerDetailsDto.getInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setJobId(motorEngineerDetailsDto.getJobId());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setClaimNo(motorEngineerDetailsDto.getClaimNo());
//
//                motorEngineerDetailsDto.getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());
//                motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());
//
//                motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());
//                motorEngineerDetailsDto.getInspectionDetailsDto().getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());
//
//                motorEngineerDetailsDto.setAssessorAllocationDto(motorEngineerDetailsDto.getAssessorAllocationDto());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorAllocationDto(motorEngineerDetailsDto.getAssessorAllocationDto());
//
//                motorEngineerDetailsDto.setTireCondtionDtoList(motorEngineerDetailsDto.getTireCondtionDtoList());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setTireCondtionDtoList(motorEngineerDetailsDto.getTireCondtionDtoList());
//                motorEngineerDetailsDto.getInspectionDetailsDto().setChassisNoConfirm(SelectionType.Pending);
//                inspectionDetailsDto = motorEngineerDetailsDto.getInspectionDetailsDto();
//                inspectionDetailsDto.setDesktopInspectionDetailsDto(motorEngineerDetailsDto.getDesktopInspectionDetailsDto());
//                inspectionDetailsDto.setTireCondtionDtoList(motorEngineerDetailsDto.getTireCondtionDtoList());
//            } else {
//                inspectionDetailsDto = inspectionDetailsService.search(refNo);
//                motorEngineerDetailsDto = motorEngineerService.search(refNo);
//            }
            claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != motorEngineerDetailsDto && AppConstant.UPDATE.equalsIgnoreCase(motorEngineerDetailsDto.getAction())) {
                request.setAttribute(AppConstant.ACTION, AppConstant.UPDATE);
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
            } else {
                request.setAttribute(AppConstant.ACTION, AppConstant.SAVE);
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                BeanUtilsBean.getInstance().copyProperties(motorEngineerDetailsDto, inspectionDetailsDto);
                cloneNestedBeans(motorEngineerDetailsDto, inspectionDetailsDto);
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setRefNo(refNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
            }

            if (assessorAllocationDto.getInspectionDto().getInspectionId() == 8) {
                motorEngineerDetailsDto.setInspectionDto(assessorAllocationDto.getInspectionDto());
            }


            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
                request.setAttribute(AppConstant.CLAIMS_DTO, claimsDto);
            }

            if (AppConstant.GARAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() ||
                    AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() ||
                    AppConstant.DESKTOP_INSPECTION_ONLINE == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                motorEngineerDetailsDto = motorEngineerService.setAdvanceAmountDetails(motorEngineerDetailsDto, claimNo);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));
            motorEngineerDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsDto.getClaimDocumentTypeDtoList());

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : claimThirdPartyDetailsGenericDtosAssessor) {
                if (claimThirdPartyDetailsGenericDto.getCcTpdId() > 0) {
                    claimThirdPartyDetailsGenericDto.setMappingId(claimThirdPartyDetailsGenericDto.getCcTpdId());
                    claimThirdPartyDetailsGenericDto.setMappingType("CALL_CENTER");
                }
            }

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosMotorEngineer = motorEngineerService.getClaimThirdPartyDetailsGeneric(claimNo);

            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new TreeMap<>((String o1, String o2) -> {
                return o2.compareTo(o1);
            });

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            list.addAll(claimThirdPartyDetailsGenericDtosMotorEngineer);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);
            motorEngineerDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, refNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }

            List<SpecialRemarkDto> specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        if (null != motorEngineerDetailsDto.getDesktopInspectionDetailsDto()
                && (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION ||
                motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION_ONLINE)) {

            if (!motorEngineerDetailsDto.getInspectionDetailsAuthStatus().equalsIgnoreCase(AppConstant.APPROVE)
                    && !(motorEngineerDetailsDto.getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus()
                    || motorEngineerDetailsDto.getRecordStatus() == ClaimStatus.INSPECTION_CHANGE_REQUESTED.getClaimStatus()
                    || motorEngineerDetailsDto.getRecordStatus() == ClaimStatus.INSPECTION_FORWARDED.getClaimStatus())) {

                motorEngineerDetailsDto = motorEngineerService.getLatestUpdateOnsiteInspectionDetails(motorEngineerDetailsDto);

            } else {
                motorEngineerDetailsDto.setDesktopInspection(AppConstant.YES);
            }
        }

        if (desktopInspectionRefNo != 0) {
            if (isSavedDesktop) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Informed");
            } else {
                request.setAttribute(AppConstant.ERROR_MESSAGE, "Inform Failed");
            }
        }
        try {
            contactDetailDto = motorEngineerService.getContactDetailForRte(motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        Integer refNo = motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo();
        String paymentStatus = assessorPaymentDetailsService.getPaymentStatus(refNo);
        motorEngineerDetailsDto.setTotalApproveAcrAmount(motorEngineerService.getTotalAprvAcrAmount(refNo));
        if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION_ONLINE &&
                AppConstant.STRING_PENDING.equals(motorEngineerDetailsDto.getInspectionDetailsAuthStatus())) {
            try {
                PolicyDto policy = callCenterService.getPolicyDetails(claimsDto.getPolRefNo());
                if (null != policy.getVehicleNumber()) {
                    claimsDto.setClaimHistory(callCenterService.getClaimHistoryForPolicyRefNo(policy.getVehicleNumber()));
                }
                claimsDto.setPolicyDto(policy);
                request.setAttribute(AppConstant.SESSION_CLAIM_DTO, claimsDto);
                updateSessionPolNo(request, response, claimsDto.getPolRefNo());
                request.setAttribute(AppConstant.policyRefNo, claimsDto.getPolRefNo());
                request.setAttribute(AppConstant.SESSION_TYPE, 8);
                request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
                request.getSession().setAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
                requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/policyView.jsp");
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        } else {
            request.setAttribute(AppConstant.CONTACT_DETAIL_DTO, contactDetailDto);
            request.setAttribute(AppConstant.PAYMENT_STATUS, paymentStatus);
            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            request.getSession().setAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
            request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp");

        }

    }

    private void cloneNestedBeans(MotorEngineerDetailsDto motorEngineerDetailsDto, InspectionDetailsDto inspectionDetailsDto) throws InvocationTargetException, IllegalAccessException {

        BeanUtilsBean beanUtilsBean = BeanUtilsBean.getInstance();
        motorEngineerDetailsDto.setOnSiteInspectionDetailsDto(new OnSiteInspectionDetailsDto());
        motorEngineerDetailsDto.setGarageInspectionDetailsDto(new GarageInspectionDetailsDto());
        motorEngineerDetailsDto.setAriInspectionDetailsDto(new ARIInspectionDetailsDto());
        motorEngineerDetailsDto.setDrSuppInspectionDetailsDto(new DrSupplementaryInspectionDetailsDto());
        motorEngineerDetailsDto.setDesktopInspectionDetailsDto(new DesktopInspectionDetailsDto());

        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto(), inspectionDetailsDto.getOnSiteInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getGarageInspectionDetailsDto(), inspectionDetailsDto.getGarageInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getAriInspectionDetailsDto(), inspectionDetailsDto.getAriInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto(), inspectionDetailsDto.getDrSuppInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getDesktopInspectionDetailsDto(), inspectionDetailsDto.getDesktopInspectionDetailsDto());
    }

    private void populateTyreConditionList(MotorEngineerDetailsDto motorEngineerDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = parameterMap.get("cot_" + i + "_" + 0)[0];
            String lf = parameterMap.get("cot_" + i + "_" + 1)[0];
            String rr = parameterMap.get("cot_" + i + "_" + 2)[0];
            String rl = parameterMap.get("cot_" + i + "_" + 3)[0];
            String rri = parameterMap.get("cot_" + i + "_" + 4)[0];
            String lri = parameterMap.get("cot_" + i + "_" + 5)[0];
            String other = parameterMap.get("cot_" + i + "_" + 6)[0];
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private void populateInitTyreConditionList(MotorEngineerDetailsDto motorEngineerDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = null;
            String lf = null;
            String rr = null;
            String rl = null;
            String rri = null;
            String lri = null;
            String other = null;
            if (0 == i) {
                rf = "N/A";
                lf = "N/A";
                rr = "N/A";
                rl = "N/A";
                rri = "N/A";
                lri = "N/A";
                other = "N/A";
            } else {
                rf = "";
                lf = "";
                rr = "";
                rl = "";
                rri = "";
                lri = "";
                other = "";
            }
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private void saveAri(HttpServletRequest request, HttpServletResponse response) {
        RequestAriDto requestAriDto = new RequestAriDto();
        try {
            MotorEngineerDetailsDto motorEngineerDetailsDto = getMotorEngineerDetailsDto(request);
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.populate(requestAriDto, request.getParameterMap());
            requestAriDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());

            RequestAriDto insert = requestAriService.insert(requestAriDto, getSessionUser(request));
            if (null != insert) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
            }
            List<RequestAriDto> list = requestAriService.searchAll(motorEngineerDetailsDto.getClaimNo());
            request.setAttribute(AppConstant.REQUESTED_ARI_LIST, list);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
        }

        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/requestARI.jsp");
    }

    private void calculateProfessionalFee(HttpServletRequest request, HttpServletResponse response) {

        try {
            String refNo = request.getParameter("refNo");
            String inspectionTypeId = request.getParameter("inspectionTypeId");
            String jobType = request.getParameter("jobType");
            String assessorFeeDetailId = request.getParameter("assessorFeeDetailId");
            String costOfCall = getCalculableAmount(request.getParameter("costOfCall"));
            String otherFee = getCalculableAmount(request.getParameter("otherFee"));
            String deductions = getCalculableAmount(request.getParameter("deductions"));
            String mileage = getCalculableAmount(request.getParameter("mileage"));

//            ClaimInspectionTypeDto claimInspectionTypeDto = inspectionDetailsService.getInspectionTypeDto(inspectionTypeId);

            BigDecimal totalFee = BigDecimal.ZERO;
            String assessorType = inspectionDetailsService.getAssessorTypeByRefNo(refNo);
            BigDecimal mileageFee;
            BigDecimal assessorFee;

            if ("1".equals(jobType)) { //WEEK
                if (AppConstant.HYBRID.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.HYBRID);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).subtract(new BigDecimal(deductions)).add(assessorFee).add(new BigDecimal(costOfCall));
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).subtract(new BigDecimal(deductions)).add(assessorFee).add(new BigDecimal(costOfCall));
                }
            } else if ("2".equals(jobType)) { //WEEKEND
                if (AppConstant.HYBRID.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.HYBRID);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).subtract(new BigDecimal(deductions)).add(assessorFee).add(new BigDecimal(costOfCall));
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).subtract(new BigDecimal(deductions)).add(assessorFee).add(new BigDecimal(costOfCall));
                }
            }

            try {
                response.getWriter().println(totalFee);
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage());
            }

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }

    }

    private void processThirdPartyDetails(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        String type = request.getParameter("TPP_TYPE");
        if ("GET".equals(type)) {
            String key = request.getParameter("KEY");
            MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION");
            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = motorEngineerDetailsDto.getThirdPartyAssessorMap();
            ClaimThirdPartyDetailsGenericDto thirdPartyDetailsGenericDto = thirdPartyAssessorMap.get(key);
            Gson gson = new Gson();
            String json = gson.toJson(thirdPartyDetailsGenericDto);
            printWriter(request, response, json);
        } else if ("ADD".equals(type)) {
            String key = request.getParameter("KEY");
            MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION");
            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = motorEngineerDetailsDto.getThirdPartyAssessorMap();

            if (null != key && !key.isEmpty()) {
                ClaimThirdPartyDetailsGenericDto thirdPartyDetailsGenericDto = thirdPartyAssessorMap.get(key);
                if ("CALL_CENTER".equals(thirdPartyDetailsGenericDto.getType())) {
                    try {
                        Map<String, String[]> parameterMap = request.getParameterMap();
                        ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDtoNew = new ClaimThirdPartyDetailsGenericDto();
                        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                            @Override
                            public Object convert(String value, Class clazz) {
                                if (clazz.isEnum()) {
                                    return Enum.valueOf(clazz, value);
                                } else {
                                    return super.convert(value, clazz);
                                }
                            }
                        });
                        beanUtilsBean.populate(claimThirdPartyDetailsGenericDtoNew, parameterMap);
                        claimThirdPartyDetailsGenericDtoNew.setType("MOTOR_ENGINEER");
                        claimThirdPartyDetailsGenericDtoNew.setStatus("NEW");
                        claimThirdPartyDetailsGenericDtoNew.setClaimNo(thirdPartyDetailsGenericDto.getClaimNo());
                        claimThirdPartyDetailsGenericDtoNew.setCcTpdId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingType("CALL_CENTER");
                        claimThirdPartyDetailsGenericDtoNew.setInpUserId(sessionUser.getUserId());
                        claimThirdPartyDetailsGenericDtoNew.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDtoNew.getType() + "_NEW_" + (thirdPartyAssessorMap.size() + 1), claimThirdPartyDetailsGenericDtoNew);
                    } catch (Exception ex) {
                        LOGGER.error(ex.getMessage());;
                    }

                } else if ("ASSESSOR".equals(thirdPartyDetailsGenericDto.getType())) {
                    try {
                        Map<String, String[]> parameterMap = request.getParameterMap();
                        ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDtoNew = new ClaimThirdPartyDetailsGenericDto();
                        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                            @Override
                            public Object convert(String value, Class clazz) {
                                if (clazz.isEnum()) {
                                    return Enum.valueOf(clazz, value);
                                } else {
                                    return super.convert(value, clazz);
                                }
                            }
                        });
                        beanUtilsBean.populate(claimThirdPartyDetailsGenericDtoNew, parameterMap);
                        claimThirdPartyDetailsGenericDtoNew.setType("MOTOR_ENGINEER");
                        claimThirdPartyDetailsGenericDtoNew.setStatus("NEW");
                        claimThirdPartyDetailsGenericDtoNew.setClaimNo(thirdPartyDetailsGenericDto.getClaimNo());
                        claimThirdPartyDetailsGenericDtoNew.setCcTpdId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingType("ASSESSOR");
                        claimThirdPartyDetailsGenericDtoNew.setInpUserId(sessionUser.getUserId());
                        claimThirdPartyDetailsGenericDtoNew.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDtoNew.getType() + "_NEW_" + (thirdPartyAssessorMap.size() + 1), claimThirdPartyDetailsGenericDtoNew);
                    } catch (Exception ex) {
                        LOGGER.error(ex.getMessage());;
                    }
                } else if ("MOTOR_ENGINEER".equals(thirdPartyDetailsGenericDto.getType())) {
                    try {
                        Map<String, String[]> parameterMap = request.getParameterMap();
                        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                            @Override
                            public Object convert(String value, Class clazz) {
                                if (clazz.isEnum()) {
                                    return Enum.valueOf(clazz, value);
                                } else {
                                    return super.convert(value, clazz);
                                }
                            }
                        });
                        beanUtilsBean.populate(thirdPartyDetailsGenericDto, parameterMap);
                        if (!"NEW".equals(thirdPartyDetailsGenericDto.getStatus())) {
                            thirdPartyDetailsGenericDto.setStatus("EDIT");
                        }
                        thirdPartyDetailsGenericDto.setInpUserId(sessionUser.getUserId());
                        thirdPartyDetailsGenericDto.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    } catch (Exception ex) {
                        LOGGER.error(ex.getMessage());;
                    }
                }
            } else {
                try {
                    Map<String, String[]> parameterMap = request.getParameterMap();
                    ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDtoNew = new ClaimThirdPartyDetailsGenericDto();
                    BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                        @Override
                        public Object convert(String value, Class clazz) {
                            if (clazz.isEnum()) {
                                return Enum.valueOf(clazz, value);
                            } else {
                                return super.convert(value, clazz);
                            }
                        }
                    });
                    beanUtilsBean.populate(claimThirdPartyDetailsGenericDtoNew, parameterMap);
                    claimThirdPartyDetailsGenericDtoNew.setType("MOTOR_ENGINEER");
                    claimThirdPartyDetailsGenericDtoNew.setStatus("NEW");
                    claimThirdPartyDetailsGenericDtoNew.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                    claimThirdPartyDetailsGenericDtoNew.setCcTpdId(0);
                    claimThirdPartyDetailsGenericDtoNew.setInpUserId(sessionUser.getUserId());
                    claimThirdPartyDetailsGenericDtoNew.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDtoNew.getType() + "_NEW_" + (thirdPartyAssessorMap.size() + 1), claimThirdPartyDetailsGenericDtoNew);
                } catch (Exception ex) {
                    LOGGER.error(ex.getMessage());;
                }
            }

            String json = "ADDED";
            printWriter(request, response, json);
        } else if ("LIST".equals(type)) {
            MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION");
            request.setAttribute("motorEngineerDto", motorEngineerDetailsDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/thirdPartyAssessorGrid.jsp");
        }
    }

    private void viewEditPrevious(HttpServletRequest request, HttpServletResponse response) {
        InspectionDetailsDto inspectionDetailsDto = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        UserDto user = getSessionUser(request);
        try {

            Integer refNo = request.getParameter("P_N_JOB_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_JOB_NO"));

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            AssessorAllocationDto assessorAllocationDto = assessorAllocationService.search(refNo);
            Integer claimNo = assessorAllocationDto.getClaimsDto().getClaimNo();


//            if (null == inspectionDetailsDto && 8 == assessorAllocationDto.getInspectionDto().getInspectionId()) {
//                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
//                motorEngineerDetailsDto.setClaimNo(claimNo);
//                motorEngineerDetailsDto.setRefNo(refNo);
//                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
//                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//                populateInitTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
//                motorEngineerService.insertDesktopInitialRecords(motorEngineerDetailsDto, user);
//            }

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            //Check whether Motor Engineer Detail Is saved in previous to this ref no
            if (null != motorEngineerDetailsDto && 0 < motorEngineerDetailsDto.getRefNo()) {
                request.setAttribute("ACTION", "UPDATE");
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
            } else {
                request.setAttribute("ACTION", "SAVE");
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                BeanUtilsBean.getInstance().copyProperties(motorEngineerDetailsDto, inspectionDetailsDto);
                cloneNestedBeans(motorEngineerDetailsDto, inspectionDetailsDto);
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setRefNo(refNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
            }

            if (assessorAllocationDto.getInspectionDto().getInspectionId() == 8) {
                motorEngineerDetailsDto.setInspectionDto(assessorAllocationDto.getInspectionDto());
            }

            if (AppConstant.GARAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() || AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                motorEngineerDetailsDto = motorEngineerService.setAdvanceAmountDetails(motorEngineerDetailsDto, claimNo);
            }

            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
                request.setAttribute(AppConstant.CLAIMS_DTO, claimsDto);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));
            motorEngineerDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsDto.getClaimDocumentTypeDtoList());

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : claimThirdPartyDetailsGenericDtosAssessor) {
                if (claimThirdPartyDetailsGenericDto.getCcTpdId() > 0) {
                    claimThirdPartyDetailsGenericDto.setMappingId(claimThirdPartyDetailsGenericDto.getCcTpdId());
                    claimThirdPartyDetailsGenericDto.setMappingType("CALL_CENTER");
                }
            }

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosMotorEngineer = motorEngineerService.getClaimThirdPartyDetailsGeneric(claimNo);

            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new TreeMap<>((String o1, String o2) -> {
                return o2.compareTo(o1);
            });

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            list.addAll(claimThirdPartyDetailsGenericDtosMotorEngineer);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);
            motorEngineerDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, refNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }

            List<SpecialRemarkDto> specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.YES);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS_PREVIOUS, motorEngineerDetailsDto);
        request.setAttribute(AppConstant.SUCCESS_MESSAGE, AppConstant.STRING_EMPTY);
        request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp");
    }


    private void calculateOnsiteInspectionValues(HttpServletRequest request, HttpServletResponse response) {

        String calType = request.getParameter("CAL_TYPE");
        try {
            BigDecimal totalFee;
            if ("ACR".equals(calType)) {
                String costPart = getCalculableAmount(request.getParameter("costPart"));
                String costLabour = getCalculableAmount(request.getParameter("costLabour"));
                totalFee = new BigDecimal(costPart).add(new BigDecimal(costLabour));
            } else if ("BaldTyrePenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String boldPercent = getCalculableAmount(request.getParameter("boldPercent"));
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(boldPercent)).divide(new BigDecimal(100));
            } else if ("UnderPenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String underPenaltyPercent = getCalculableAmount(request.getParameter("underPenaltyPercent"));
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(underPenaltyPercent)).divide(new BigDecimal(100));
            } else if ("PayableAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String excess = getCalculableAmount(request.getParameter("excess"));
                String underPenaltyAmount = getCalculableAmount(request.getParameter("underPenaltyAmount"));
                String boldTirePenaltyAmount = getCalculableAmount(request.getParameter("boldTirePenaltyAmount"));
                totalFee = new BigDecimal(acr).subtract((new BigDecimal(excess)).add(new BigDecimal(underPenaltyAmount).add(new BigDecimal(boldTirePenaltyAmount))));
            } else {
                totalFee = BigDecimal.ZERO;
            }

            try {
                response.getWriter().println(totalFee.toString());
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage());;
            }

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private String getCalculableAmount(String parameter) {
        if (null != parameter && !parameter.isEmpty()) {
            return parameter.replaceAll(",", "");
        } else {
            return "0";
        }
    }

    private void deleteImages(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        UserDto sessionUser = getSessionUser(request);
        try {
            String images = request.getParameter("deleteImages");

            Integer claimNo = Integer.valueOf(request.getParameter("claimNo"));
            Integer jobRefNo = Integer.valueOf(request.getParameter("jobRefNo"));

            //Integer refNo = Integer.valueOf(request.getParameter("refNo"));
            // Integer jobRefNo = Integer.valueOf(request.getParameter("jobRefNo"));
            //  ClaimImageDto claimImageDto = stDocumentService.getClaimImageDto(refNo);
            boolean deleted = stDocumentService.deleteImages(images, claimNo, sessionUser, jobRefNo);
            if (deleted) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void docDeltete(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        try {
            HttpSession session = request.getSession();
            UserDto user = getSessionUser(request);
            String docs = request.getParameter("deleteDoc");
            Integer documentTypeId = Integer.parseInt(request.getParameter("documentTypeId"));
            MotorEngineerDetailsDto motorEngineerDetailsDto = getMotorEngineerDetailsDto(request);
            InspectionDetailsDto inspectionDetailsDto = motorEngineerDetailsDto.getInspectionDetailsDto();

            boolean deleted = stDocumentService.deleteDocuments(docs, documentTypeId, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), user);
            if (deleted) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully deleted");
            }

            if (inspectionDetailsDto != null) {
                Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                List<ClaimUploadViewDto> claimUploadViewDtoList = inspectionDetailsService.getClaimUploadViewDtoList(inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionTypeId);
                session.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            }
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/documentUpload.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Can not be deleted");
        }
    }

    private void approveDetails(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        BigDecimal premOutstand = null == request.getParameter("outstand") || request.getParameter("outstand").isEmpty() ? BigDecimal.ZERO : new BigDecimal(request.getParameter("outstand"));
        String policyType = null == request.getParameter("isCancelled") ? AppConstant.STRING_EMPTY : request.getParameter("isCancelled");
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        boolean forward = AppConstant.YES.equals((String) request.getParameter("FORWARD"));
        String onsiteReview = request.getParameter("onsiteReview");
        motorEngineerDetailsDto.setIsOnsiteReviewApply(
                (onsiteReview == null || onsiteReview.trim().isEmpty()) ? AppConstant.NO : AppConstant.YES
        );

        String dispatchUrl;
        ContactDetailDto contactDetailDto = null;
        try {
            //Configure Bean Utils
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.getConvertUtils().register(false, false, 0);

            beanUtilsBean.populate(motorEngineerDetailsDto, request.getParameterMap());

            if (ConditionType.No == motorEngineerDetailsDto.getFirstStatementReq()) {
                motorEngineerDetailsDto.setFirstStatementReqReason(AppConstant.STRING_EMPTY);
            }

            if (!AppConstant.AUTH_ASSESSOR_FEE.equals(actionType)) {
                motorEngineerService.isValidAuthorize(motorEngineerDetailsDto, forward, email);
            }

            if (motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 5
                    && motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 6
                    && motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 7
                    && motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 9
                    && AppConstant.YES.equals(motorEngineerDetailsDto.getInspectionDetailsDto().getIsVehicleAvailable())) {
                populateTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
            }

            motorEngineerDetailsDto.setActionType(actionType);
            if (!forward) {
                if (AppConstant.AUTH_INSPECTION_DETAILS.equals(actionType)) {
                    motorEngineerDetailsDto.setInspectionDetailsAuthStatus(AppConstant.APPROVE);
                    motorEngineerDetailsDto.setInspectionDetailsAuthUserId(sessionUser.getUserId());
                    motorEngineerDetailsDto.setInspectionDetailsAuthDatetime(Utility.sysDateTime());
                } else if (AppConstant.AUTH_ASSESSOR_FEE.equals(actionType)) {
                    motorEngineerDetailsDto.setAssessorFeeAuthStatus(AppConstant.APPROVE);
                    motorEngineerDetailsDto.setAssessorFeeAuthUserId(sessionUser.getUserId());
                    motorEngineerDetailsDto.setAssessorFeeAuthDatetime(Utility.sysDateTime());
                }
            } else {
                motorEngineerDetailsDto.setInspectionDetailsAuthStatus(AppConstant.STRING_PENDING);
                motorEngineerDetailsDto.setInspectionDetailsAuthUserId(sessionUser.getUserId());
                motorEngineerDetailsDto.setInspectionDetailsAuthDatetime(Utility.sysDateTime());
            }
            motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorFeeAuthStatus(motorEngineerDetailsDto.getAssessorFeeAuthStatus());
            motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorFeeAuthUserId(motorEngineerDetailsDto.getAssessorFeeAuthUserId());
            motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorFeeAuthDatetime(motorEngineerDetailsDto.getAssessorFeeAuthDatetime());
            motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthStatus(motorEngineerDetailsDto.getInspectionDetailsAuthStatus());
            motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthUserId(motorEngineerDetailsDto.getInspectionDetailsAuthUserId());
            motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthDatetime(motorEngineerDetailsDto.getInspectionDetailsAuthDatetime());

//            motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(motorEngineerDetailsDto.getRecordStatus());

            Integer refNo = motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo();

            int successCode;
            switch (action) {
                case "SAVE":
                    motorEngineerService.insert(motorEngineerDetailsDto, sessionUser, forward, premOutstand, policyType.equals(AppConstant.CANCELLED_POLICY));
                    if ("SAVE_REPORT".equals(actionType)) {
                        successCode = 1;

                    } else if (forward) {
                        successCode = 3;
                    } else {
                        successCode = 2;
                    }
                    response.sendRedirect(request.getContextPath() + "/MotorEngineerController" +
                            "/viewEdit?P_N_REF_NO=" + refNo + "&successCode=" + successCode);
                    return;
                case "UPDATE":
                    motorEngineerService.update(motorEngineerDetailsDto, sessionUser, forward, premOutstand, policyType.equals(AppConstant.CANCELLED_POLICY));
                    if ("SAVE_REPORT".equals(actionType)) {
                        successCode = 1;
                    } else if (forward) {
                        successCode = 3;
                    } else {
                        successCode = 2;
                    }
                    response.sendRedirect(request.getContextPath() + "/MotorEngineerController" +
                            "/viewEdit?P_N_REF_NO=" + refNo + "&successCode=" + successCode);
                    return;
            }

            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
        } catch (ErrorMsgException e) {
            switch (action) {
                case "SAVE":
                    request.setAttribute(e.getField(), e.getErrorMessage());
                    request.setAttribute("ACTION", "SAVE");
                    break;
                case "UPDATE":
                    request.setAttribute(e.getField(), e.getErrorMessage());
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";

        } catch (WrongValueException e) {
            switch (action) {
                case "SAVE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, e.getErrorMessage());
                    request.setAttribute("ACTION", "SAVE");
                    break;
                case "UPDATE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, e.getErrorMessage());
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }
            request.setAttribute("fieldsEnable", AppConstant.YES);
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";

        } catch (UserNotFoundException e) {
            switch (action) {
                case "SAVE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "User not found to assign claim / inspection");
                    request.setAttribute("ACTION", "SAVE");
                    break;
                case "UPDATE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "User not found to assign claim / inspection");
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            switch (action) {
                case "SAVE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be approved");
                    request.setAttribute("ACTION", "SAVE");
                    break;
                case "UPDATE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be approved");
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(ClaimStatus.APPROVED.getClaimStatus());
        }
//        motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(motorEngineerDetailsDto.getRecordStatus());100
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void recallDesktop(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);

        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        request.setAttribute("ACTION", action);
        request.setAttribute("ACTION_TYPE", actionType);

        String dispatchUrl;
        try {
            motorEngineerService.recallDesktop(motorEngineerDetailsDto, sessionUser);
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionList.jsp";
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Recalled");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Recall Failed");
        }
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void informDesktop(HttpServletRequest request, HttpServletResponse response) {
        boolean isSaved = true;
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformToCustomerName(request.getParameter("desktopInspectionDetailsDto.informToCustomerName"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformToCustomerContact(request.getParameter("desktopInspectionDetailsDto.informToCustomerContact"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformToGarageName(request.getParameter("desktopInspectionDetailsDto.informToGarageName"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformToGarageContact(request.getParameter("desktopInspectionDetailsDto.informToGarageContact"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setIsAgreeGarage(request.getParameter("desktopInspectionDetailsDto.isAgreeGarage"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setIsAgreeCustomer(request.getParameter("desktopInspectionDetailsDto.isAgreeCustomer"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setDesktopComment(request.getParameter("desktopInspectionDetailsDto.desktopComment"));
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setReasonForDisagree(request.getParameter("desktopInspectionDetailsDto.reasonForDisagree"));

        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        request.setAttribute("ACTION", action);
        request.setAttribute("ACTION_TYPE", actionType);

        try {
            if (("Agree").equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeGarage()) && ("Agree").equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeCustomer())) {
                Boolean isApprovedOnOrOffSiteInspection = true;
                if (AppConstant.DESKTOP_INSPECTION == motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                    isApprovedOnOrOffSiteInspection = motorEngineerService.isOnSiteOrOffSIteInspectionApproved(motorEngineerDetailsDto.getClaimNo());
                }
                if (isApprovedOnOrOffSiteInspection) {
                    motorEngineerService.informDesktop(motorEngineerDetailsDto, sessionUser);
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Informed");
                } else {
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not submit. Pending On-site / Off-site inspection review exists for this claim.");
                }
            } else {
                motorEngineerService.informDesktop(motorEngineerDetailsDto, sessionUser);
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Informed");
            }

        } catch (Exception e) {
            isSaved = false;
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Inform Failed");
        }

        Integer refNo = motorEngineerDetailsDto.getRefNo();
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionList.jsp");
//        this.viewEdit(request, response, refNo, isSaved);

    }

    private void forwardToInformDesktop(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        request.setAttribute("ACTION", action);
        request.setAttribute("ACTION_TYPE", actionType);
        String dispatchUrl;
        try {

            String forwardUserName = request.getParameter("forwardToInformDesktopUser");
            motorEngineerService.forwardToInformDesktop(motorEngineerDetailsDto, forwardUserName, sessionUser);
//            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionList.jsp";
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Forward");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Forward Failed");
        }
        motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(33);
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void taskReturnDesktop(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        request.setAttribute("ACTION", action);
        request.setAttribute("ACTION_TYPE", actionType);
        String dispatchUrl;
        try {
            motorEngineerService.recallDesktop(motorEngineerDetailsDto, sessionUser);
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionList.jsp";
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Returned");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Return Failed");
        }
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void returnDesktop(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) request.getSession().getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        String action = (String) request.getParameter("ACTION");
        String actionType = (String) request.getParameter("ACTION_TYPE");
        request.setAttribute("ACTION", action);
        request.setAttribute("ACTION_TYPE", actionType);
        String dispatchUrl;
        try {

            motorEngineerService.returnDesktop(motorEngineerDetailsDto, sessionUser);
            motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(34);
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Rejected");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp";
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Failed to Reject");
        }
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS + "_SESSION", motorEngineerDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void calculateUnderInsPenaltyPerc(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal percentage;
        try {

            String pav = getCalculableAmount(request.getParameter("pav"));
            String sumInsured = getCalculableAmount(request.getParameter("sumInsured"));

            BigDecimal bigDecimalHundred = new BigDecimal(100);

            percentage = new BigDecimal(pav).multiply(new BigDecimal(80)).divide(bigDecimalHundred)
                    .subtract(new BigDecimal(sumInsured))
                    .divide(new BigDecimal(pav).multiply(new BigDecimal(80)).divide(bigDecimalHundred), 2, RoundingMode.HALF_UP)
                    .multiply(bigDecimalHundred);

        } catch (ArithmeticException e) {
            percentage = BigDecimal.ZERO;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
            percentage = BigDecimal.ZERO;
        }

        try {
            response.getWriter().println(percentage.toString());
        } catch (IOException ex) {
            LOGGER.error(ex.getMessage());;
        }

    }

    private void getTcUserList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();
            List<UserDto> list = assessorAllocationService.getTCList();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }

    }

    private void viewEditClaimPrevious(HttpServletRequest request, HttpServletResponse response) {
        InspectionDetailsDto inspectionDetailsDto = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        ContactDetailDto contactDetailDto = null;
        UserDto user = getSessionUser(request);
        boolean checkIsGarageType = false;
        boolean isReadOnly = false;
        boolean isEngDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            Integer refNo = request.getParameter("P_N_JOB_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_JOB_NO"));
            //  String jobNo = request.getParameter("P_N_JOB_NO") == null ? "0" : request.getParameter("P_N_JOB_NO");
            Integer polNo = request.getParameter("P_POL_N_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_N_REF_NO"));

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);
            AssessorAllocationDto assessorAllocationDto = assessorAllocationService.search(refNo);


            if (assessorAllocationDto.getInspectionDto().getInspectionId() == 1 || assessorAllocationDto.getInspectionDto().getInspectionId() == 2) {
                //checkIsGarageType = assessorAllocationService.checkValidInspectionType(claimNo, AppConstant.GARAGE_INSPECTION);
                checkIsGarageType = false;
            }


//            if (null == inspectionDetailsDto && 8 == assessorAllocationDto.getInspectionDto().getInspectionId()) {
//                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
//                motorEngineerDetailsDto.setClaimNo(claimNo);
//                motorEngineerDetailsDto.setRefNo(refNo);
//                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
//                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//                populateInitTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
//                motorEngineerService.insertDesktopInitialRecords(motorEngineerDetailsDto, user);
//            }

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            //Check whether Motor Engineer Detail Is saved in previous to this ref no
            if (null != motorEngineerDetailsDto && 0 < motorEngineerDetailsDto.getRefNo()) {
                request.setAttribute("ACTION", "UPDATE");
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
            } else {
                request.setAttribute("ACTION", "SAVE");
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                BeanUtilsBean.getInstance().copyProperties(motorEngineerDetailsDto, inspectionDetailsDto);
                cloneNestedBeans(motorEngineerDetailsDto, inspectionDetailsDto);
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setRefNo(refNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
            }

            if (assessorAllocationDto.getInspectionDto().getInspectionId() == 8) {
                motorEngineerDetailsDto.setInspectionDto(assessorAllocationDto.getInspectionDto());
            }

            if (AppConstant.GARAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() ||
                    AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() || DESKTOP_INSPECTION_ONLINE == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                motorEngineerDetailsDto = motorEngineerService.setAdvanceAmountDetails(motorEngineerDetailsDto, claimNo);
            }

            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
                request.setAttribute(AppConstant.CLAIMS_DTO, claimsDto);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));
            motorEngineerDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsDto.getClaimDocumentTypeDtoList());

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : claimThirdPartyDetailsGenericDtosAssessor) {
                if (claimThirdPartyDetailsGenericDto.getCcTpdId() > 0) {
                    claimThirdPartyDetailsGenericDto.setMappingId(claimThirdPartyDetailsGenericDto.getCcTpdId());
                    claimThirdPartyDetailsGenericDto.setMappingType("CALL_CENTER");
                }
            }

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosMotorEngineer = motorEngineerService.getClaimThirdPartyDetailsGeneric(claimNo);

            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new TreeMap<>((String o1, String o2) -> {
                return o2.compareTo(o1);
            });

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            list.addAll(claimThirdPartyDetailsGenericDtosMotorEngineer);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);
            motorEngineerDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, refNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }

            isReadOnly = claimHandlerService.isForwardedToEngineer(claimNo, user.getUserId());

            List<SpecialRemarkDto> specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            contactDetailDto = motorEngineerService.getContactDetailForRte(motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser());

            request.setAttribute(AppConstant.IS_READ_ONLY, isReadOnly);
            request.setAttribute(AppConstant.CONTACT_DETAIL_DTO, contactDetailDto);
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        if (null != inspectionDetailsDto) {
            if (inspectionDetailsDto.getRecordStatus() == ClaimStatus.APPROVED.getClaimStatus()) {
                request.setAttribute(AppConstant.CHANGED, AppConstant.NO);
            } else {
                request.setAttribute(AppConstant.CHANGED, AppConstant.YES);
            }
        }

        if (isEngDocUpload) {
            request.setAttribute(AppConstant.CHANGED, AppConstant.YES);
        }

        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS_PREVIOUS, motorEngineerDetailsDto);
        // request.getSession().setAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.setAttribute(AppConstant.SUCCESS_MESSAGE, AppConstant.STRING_EMPTY);
        request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
        request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.YES);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp");
    }

    private void updateChangeRequest(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        try {
            Integer refNo = request.getParameter("refNo") == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("refNo"));
            Integer claimNo = request.getParameter("claimNo") == null || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            UserDto user = getSessionUser(request);
            boolean isReadOnly = claimHandlerService.isForwardedToEngineer(claimNo, user.getUserId());
            boolean updated = false;
            if (!isReadOnly) {
                updated = motorEngineerService.updateToChangeRequest(refNo, remark, user, true);
            }
            if (updated) {
                json = "SUCCESS";
//                json = "Successfully Change Request requested";
            } else if (isReadOnly) {
                json = AppConstant.STRING_EMPTY;
            } else {
                json = "FAILED";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateAsChangeRequest(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        try {
            Integer refNo = request.getParameter("refNo") == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("refNo"));
            Integer claimNo = request.getParameter("claimNo") == null || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            UserDto user = getSessionUser(request);
            boolean isReadOnly = claimHandlerService.isForwardedToEngineer(claimNo, user.getUserId());
            boolean updated = false;
            if (!isReadOnly) {
                updated = motorEngineerService.updateToChangeRequest(refNo, remark, user, false);
            }
            if (updated) {
                json = "Successfully Change Request requested";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }


    private InspectionDetailsDto getInspectionDetailsDto(HttpServletRequest request, HttpServletResponse response, String historyRecord) {
        InspectionDetailsDto inspectionDetailsDto = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        try {
            if (historyRecord.equals(AppConstant.YES)) {
                Integer jobRefNo = Integer.parseInt(request.getParameter("JOB_REF_NO") == null ? AppConstant.ZERO : request.getParameter("JOB_REF_NO"));
                inspectionDetailsDto = inspectionDetailsService.search(jobRefNo);

            } else {
                motorEngineerDetailsDto = getMotorEngineerDetailsDto(request);
                inspectionDetailsDto = motorEngineerDetailsDto.getInspectionDetailsDto();
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private void viewDocumentUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            String prevInspection = request.getParameter(AppConstant.PREVIOUS_INSPECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PREVIOUS_INSPECTION);
            if (AppConstant.YES.equals(prevInspection)) {
                request.setAttribute(AppConstant.PREVIOUS_INSPECTION, prevInspection);
            }
            claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, "'ESTIMATE','APPROVED_ESTIMATE','OTHER'");
            request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/documentUpload.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewBillUpload(HttpServletRequest request, HttpServletResponse response) {
        boolean pendingInspection = false;
        try {
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            String prevInspection = request.getParameter(AppConstant.PREVIOUS_INSPECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PREVIOUS_INSPECTION);
            pendingInspection = motorEngineerService.checkPendingInspection(claimId);
            if (AppConstant.YES.equals(prevInspection)) {
                request.setAttribute(AppConstant.PREVIOUS_INSPECTION, prevInspection);
            }
            claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, "'BILL','APPROVED_ESTIMATE'");
            request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute("ENG_DOC", false);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/billDocumentUploadView.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void isForward(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        UserDto sessionUser = getSessionUser(request);
        try {
            BigDecimal acr = new BigDecimal(request.getParameter("acr"));
            Integer refNo = null == request.getParameter("refNo") || request.getParameter("refNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("refNo"));
            Integer inspectionId = null == request.getParameter("inspectionType") || request.getParameter("inspectionType").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("inspectionType"));
            boolean forwardInspection = motorEngineerService.isForwardInspection(acr, refNo, inspectionId, sessionUser);

            if (forwardInspection) {
                json = "SUCCESS";
            } else {
                json = "FAIL";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void getOnMiSiteOnlineAssessmentData(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            BufferedReader reader = request.getReader();
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }

            OnMiSiteReqDto onMiSiteReqDto = objectMapper.readValue(jsonBuilder.toString(), OnMiSiteReqDto.class);
            UserDto user = getSessionUser(request);
            String url = motorEngineerService.callOnMiSiteOnlineAssessment(onMiSiteReqDto, user);

            response.setStatus(200);
            response.setContentType(RESPONSE_CONTENT_TYPE);
            response.setCharacterEncoding(RESPONSE_CHARSET);
            response.getWriter().write(url);

        } catch (Exception e) {
            response.setStatus(500);
            LOGGER.error("Error in getOnMiSiteOnlineAssessmentData: " + e.getMessage(), e);
            LOGGER.error(e.getMessage(), e);
        }
    }

}
