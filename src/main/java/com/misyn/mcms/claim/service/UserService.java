package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.UserMasterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface UserService {

    UserMasterDto saveClaimUser(UserMasterDto userDto) throws MisynJDBCException;

    UserMasterDto updateClaimUser(UserMasterDto userDto) throws MisynJDBCException;

    DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception;
}
